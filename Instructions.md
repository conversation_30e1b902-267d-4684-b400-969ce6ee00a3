You are an expert full-stack developer specializing in React, TypeScript, and modern frontend architectures. Your task is to act as a development lead and provide a comprehensive plan and implementation details for extending an existing Restaurant Management System (RMS) dashboard.

**1. Project Context & Core Technologies**

You will be working on the "AlpAI Management Dashboard," a single-page application built with a modern tech stack.

*   **Core Purpose:** To provide a comprehensive admin interface for managing all aspects of a restaurant's operations.
*   **Frontend:** React (with TypeScript)
*   **Build Tool:** Vite
*   **Routing:** React Router
*   **State Management:** Zustand
*   **Styling:** Tailwind CSS with a pre-built component library (similar to Shadcn/ui) located in `app/components/ui/`.
*   **Schema Validation:** Zod
*   **Icons:** Lucide React
*   **Architecture:** The application follows a modular structure, with clear separation of concerns. Key directories include:
    *   `app/components/[feature]`: Components for each feature (e.g., `employees`, `orders`).
    *   `app/routes/`: Route definitions.
    *   `app/store/`: Global Zustand store.
    *   `app/types/`: TypeScript type definitions.
    *   `app/data/`: Mock data for development.

**2. High-Level Goal**

The primary goal is to evolve the existing dashboard into a more complete RMS by adding several critical new features and enhancing existing ones, ensuring all new development seamlessly integrates with the current architecture and UI/UX conventions.

**3. New Feature Implementation Plan**

You are to plan and implement the following new features:

**A. User Authentication (Registration & Login)**
*   **Objective:** Implement a complete authentication flow. Since this is a management system, the primary flow will be login for existing staff. Registration might be an admin-only feature.
*   **Tasks:**
    1.  Create a new `auth` module within `app/components/`.
    2.  Develop `LoginForm.tsx` and `RegistrationForm.tsx` components.
    3.  Implement a new Zustand slice (`authSlice`) in the main store to manage user session, token, and profile information.
    4.  Create protected routes using React Router to restrict access to the main dashboard.
    5.  Create a public-facing `/login` route.
    6.  For now, simulate authentication using the mock data in `app/data/sample.ts`.

**B. Supplier Management**
*   **Objective:** Create a full CRUD module for managing food and equipment suppliers.
*   **Tasks:**
    1.  Create a new `Supplier.ts` type definition in `app/types/` with fields like `id`, `name`, `contactPerson`, `phone`, `email`, `address`, and `productsSupplied` (array of strings).
    2.  Create a new `suppliers` module in `app/components/`.
    3.  Develop `SupplierList.tsx` to display suppliers in a data table (use the existing `app/components/ui/table` component).
    4.  Develop `SupplierForm.tsx` for creating and editing suppliers, using the existing `app/components/ui/` components (Input, Button, etc.) and Zod for validation.
    5.  Add supplier state management to the Zustand store.
    6.  Create a new `/suppliers` route in `app/routes.ts`.

**C. Detailed Stock & Inventory Management**
*   **Objective:** Enhance the existing "Ingredients" module to provide more robust inventory tracking.
*   **Tasks:**
    1.  Modify the `Ingredient.ts` type in `app/types/` to include `stockLevel`, `unitOfMeasure` (e.g., 'kg', 'liters', 'units'), `lowStockThreshold`, and `supplierId`.
    2.  Update the `IngredientForm.tsx` to allow setting these new fields.
    3.  In the `IngredientsList.tsx`, visually indicate items that are below their `lowStockThreshold`.
    4.  Create a new component, `StockAdjustmentForm.tsx`, that allows users to log additions or removals from stock, creating a history of adjustments (for now, this history can be stored in the Zustand store).

**D. Kitchen Display System (KDS) View**
*   **Objective:** Create a simplified, real-time view for kitchen staff to see and manage incoming orders.
*   **Tasks:**
    1.  Create a new `kds` module in `app/components/`.
    2.  Develop a `KDSView.tsx` component.
    3.  The view should display active orders as cards, showing the table number, items in the order, and time since the order was placed.
    4.  Each order card should have buttons to update its status (e.g., "Preparing", "Ready", "Completed"). These actions should update the global order state in Zustand.
    5.  Create a new `/kitchen` route.

**4. Architectural & UI/UX Mandates**

*   **Consistency is Key:** Strictly adhere to the existing project structure, naming conventions, and coding style.
*   **Component Reusability:** Leverage the existing UI component library in `app/components/ui/` for all new UI elements. Do not introduce new, one-off styles or components where a reusable one exists.
*   **State Management:** All application state must be managed through the central Zustand store. Create new slices for new features as needed.
*   **Data Flow:** Follow the established pattern of components fetching data from the Zustand store and using actions to update it.
*   **Styling:** Use Tailwind CSS utility classes for all styling.
*   **Validation:** All forms must have robust client-side validation using Zod.

**5. Final Deliverable**

Provide a comprehensive set of code changes required to implement the features outlined above. This should include:
*   The full content for all new files to be created.
*   The necessary modifications/diffs for any existing files that need to be changed.

Begin by outlining the steps you will take, starting with the creation of the `Supplier.ts` type definition.
