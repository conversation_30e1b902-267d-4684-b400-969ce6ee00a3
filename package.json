{"name": "alpai-fe", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@react-router/node": "^7.4.0", "@react-router/serve": "^7.4.0", "@tanstack/react-query": "^5.71.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "isbot": "^5.1.17", "lucide-react": "^0.485.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.6.4", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-router": "^7.4.0", "sonner": "^2.0.2", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.5", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@react-router/dev": "^7.4.0", "@tailwindcss/vite": "^4.0.0", "@types/node": "^20", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "react-router-devtools": "^1.1.0", "tailwindcss": "^4.0.0", "typescript": "^5.7.2", "vite": "^5.4.11", "vite-tsconfig-paths": "^5.1.4"}}