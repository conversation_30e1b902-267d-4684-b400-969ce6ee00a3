# Docker Setup for AlpAI Frontend

This document provides instructions for running the AlpAI Frontend application using Docker in both development and production environments.

## Prerequisites

- [Docker](https://docs.docker.com/get-docker/)
- [Docker Compose](https://docs.docker.com/compose/install/)

## Development Environment

The development environment uses a Docker container with hot-reloading enabled, allowing you to make changes to the code and see them reflected immediately.

### Starting the Development Environment

```bash
# Start the development container
docker-compose up app-dev

# Or run in detached mode
docker-compose up -d app-dev
```

This will:
- Build the Docker image using `Dockerfile.dev`
- Start a container with the application running in development mode
- Mount your local code into the container for hot-reloading
- Expose the application on port 5173

Access the application at: http://localhost:5173

### Stopping the Development Environment

```bash
# Stop the container
docker-compose down
```

## Production Environment

The production environment uses a multi-stage build to create an optimized Docker image with Nginx serving the static files.

### Starting the Production Environment

```bash
# Build and start the production container
docker-compose up app-prod

# Or run in detached mode
docker-compose up -d app-prod
```

This will:
- Build the Docker image using the production `Dockerfile`
- Create an optimized production build
- Serve the application using Nginx
- Expose the application on port 8080

Access the application at: http://localhost:8080

### Stopping the Production Environment

```bash
# Stop the container
docker-compose down
```

## Building Images Manually

If you prefer to build and run the Docker images manually:

### Development Image

```bash
# Build the development image
docker build -t alpai-frontend-dev -f Dockerfile.dev .

# Run the development container
docker run -p 5173:5173 -v $(pwd):/app -v /app/node_modules --name alpai-frontend-dev alpai-frontend-dev
```

### Production Image

```bash
# Build the production image
docker build -t alpai-frontend-prod .

# Run the production container
docker run -p 8080:80 --name alpai-frontend-prod alpai-frontend-prod
```

## Additional Commands

```bash
# View logs for a specific service
docker-compose logs app-dev
docker-compose logs app-prod

# Enter the container shell
docker-compose exec app-dev sh
docker-compose exec app-prod sh

# Rebuild images
docker-compose build app-dev
docker-compose build app-prod
```

## Environment Variables

You can add environment variables in the `docker-compose.yml` file or create a `.env` file for more sensitive information.

## Troubleshooting

### Port Conflicts

If you encounter port conflicts, you can change the exposed ports in the `docker-compose.yml` file:

```yaml
ports:
  - "3000:5173"  # Maps host port 3000 to container port 5173
```

### Volume Mounting Issues

If you experience issues with volume mounting on Windows, try using the following format in `docker-compose.yml`:

```yaml
volumes:
  - ./:/app:cached
```

### Node Modules Issues

If you encounter issues with node_modules, you can rebuild the container:

```bash
docker-compose down
docker-compose build --no-cache app-dev
docker-compose up app-dev
```
