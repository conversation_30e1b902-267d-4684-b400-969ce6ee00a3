# AlpAI Management Dashboard

A modern, full-featured management dashboard for restaurant and hospitality businesses. This project provides a comprehensive admin interface for managing tables, spaces, menu categories, menu items, employees, roles, departments, orders, and ingredients.

## Features

- **Dashboard Overview**: Visualize key business metrics and statistics.
- **Table Management**: Create, edit, assign spaces, and manage statuses for tables.
- **Space Management**: Organize your restaurant into spaces (e.g., Main Dining, Patio).
- **Menu Management**: CRUD for menu categories and items, with support for nested categories.
- **Order Management**: Create, edit, and track orders with itemized details, table assignment, and payment status.
- **Employee & Role Management**: Manage staff, assign roles, and control permissions.
- **Department Management**: Organize employees and roles by department.
- **Ingredient & Inventory Management**: Track ingredients, categories, allergens, and stock status.
- **Responsive UI**: Works well on desktop and tablets.
- **Error Boundaries**: Robust error handling for all routes.
- **Modern UI**: Built with Tailwind CSS and a custom component library.

## Technologies Used

- **React** (with TypeScript)
- **Vite** (for fast development and builds)
- **React Router** (client-side routing)
- **Zustand** (state management)
- **Zod** (schema validation)
- **Tailwind CSS** (utility-first styling)
- **Lucide React** (icon set)
- **Docker & Docker Compose** (containerization, optional)
- **Nginx** (static file serving, optional)

## Project Structure

```text
app/
  components/         # All UI and feature components
    layout/           # Layout, Sidebar, Header
    tables/           # TableForm, TableList, Tables
    spaces/           # SpaceForm, SpaceList, Spaces
    menu/             # Menu, MenuCategoryForm, MenuCategoryList, MenuForm, MenuList
    orders/           # OrderForm, OrderList, OrderDetails, Orders
    employees/        # EmployeeForm, EmployeeList, Employees
    roles/            # RoleForm, RoleList, Roles
    departments/      # DepartmentForm, DepartmentList, Departments
    ingredients/      # IngredientForm, IngredientList, IngredientCategories, etc.
    ui/               # Reusable UI components (Button, Input, Select, etc.)
    errors/           # ErrorBoundary, NotFound
  data/               # Sample/mock data
  store/              # Zustand stores
  types/              # TypeScript types
  lib/                # Utility functions
  routes/             # Route definitions
  styles/             # Custom CSS
public/                # Static assets
build/                 # Production build output
```

## Getting Started

### Prerequisites

- Node.js (v18+ recommended)
- npm or yarn

### Installation

1. Clone the repository:

   ```sh
   git clone <repo-url>
   cd frontend
   ```

2. Install dependencies:

   ```sh
   npm install
   # or
   yarn install
   ```

### Running the App (Development)

```sh
npm run dev
# or
yarn dev
```

The app will be available at `http://localhost:5173` (or as shown in your terminal).

### Building for Production

```sh
npm run build
npm run preview
```

### Docker Support

- Build and run with Docker:

  ```sh
  docker build -t alpai-frontend .
  docker run -p 80:80 alpai-frontend
  ```

- Or use Docker Compose:

  ```sh
  docker-compose up --build
  ```

## Customization & Extending

- Add new features by creating components in `app/components/` and updating the relevant store/types.
- UI components are reusable and can be found in `app/components/ui/`.
- State is managed globally with Zustand (`app/store/store.ts`).
- Sample/mock data is in `app/data/sample.ts`.

## Contributing

Pull requests are welcome! For major changes, please open an issue first to discuss what you would like to change.

---

**AlpAI Management Dashboard** — Modern, modular, and ready for your business.
