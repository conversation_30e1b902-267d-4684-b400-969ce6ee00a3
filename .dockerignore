# Dependencies
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Build files
build
dist
.cache
.react-router

# Version control
.git
.gitignore

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.idea
.vscode
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Docker files
Dockerfile
Dockerfile.dev
docker-compose.yml
.dockerignore

# Documentation
README.md
LICENSE

# Testing
coverage