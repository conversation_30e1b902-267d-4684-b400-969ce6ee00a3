services:
  # Development service
  frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: alpai-frontend-dev
    ports:
      - "5173:5173"
    volumes:
      - ./:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    restart: unless-stopped
    profiles:
      - dev

  # Production service
  frontend-prod:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: alpai-frontend-prod
    ports:
      - "8080:80"
    restart: unless-stopped
    profiles:
      - prod
    # Uncomment to add environment variables if needed
    # environment:
    #   - NODE_ENV=production
