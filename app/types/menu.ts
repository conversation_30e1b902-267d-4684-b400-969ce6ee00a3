export type MenuItemStatus = 'Available' | 'Unavailable' | 'Seasonal';

export interface MenuCategory {
  id: string;
  name: string;
  description: string;
  order: number;
  parentId?: string; // Optional: ID of the parent category
}

export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  categoryId: string;
  category?: string; // For backward compatibility
  image?: string;
  ingredients: string[];
  allergens: string[];
  nutritionalInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
  };
  status: MenuItemStatus;
  isVegetarian: boolean;
  isVegan: boolean;
  isGlutenFree: boolean;
  preparationTime: number; // in minutes
}
