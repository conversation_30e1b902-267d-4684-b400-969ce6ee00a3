import { Employee } from './employee';
import { MenuItem } from './menu';

export type OrderStatus = 
  | 'Pending' 
  | 'In Progress' 
  | 'Ready' 
  | 'Delivered' 
  | 'Completed' 
  | 'Cancelled';

export type PaymentStatus = 
  | 'Unpaid' 
  | 'Paid' 
  | 'Refunded' 
  | 'Partial';

export type PaymentMethod = 
  | 'Cash' 
  | 'Credit Card' 
  | 'Debit Card' 
  | 'Mobile Payment' 
  | 'Gift Card';

export type OrderType = 
  | 'Dine-in' 
  | 'Takeout' 
  | 'Delivery' 
  | 'Catering';

export interface OrderItem {
  id: string;
  menuItemId: string;
  menuItem?: MenuItem; // Optional reference to the full menu item
  quantity: number;
  price: number; // Price at the time of order (may differ from current menu price)
  notes?: string;
  modifiers?: string[]; // e.g., "No onions", "Extra cheese"
}

export interface Order {
  id: string;
  orderNumber: string;
  tableNumber?: string; // For dine-in orders
  tableId?: string; // Optional: ID of the table associated with the order
  tableName?: string; // Optional: Name of the table, can be denormalized
  customerId?: string; // For registered customers
  customerName: string;
  customerPhone?: string;
  customerEmail?: string;
  orderType: OrderType;
  status: OrderStatus;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  tip?: number;
  discount?: number;
  total: number;
  paymentStatus: PaymentStatus;
  paymentMethod?: PaymentMethod;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  completedAt?: string; // ISO date string
  assignedTo?: string; // Employee ID
  assignedEmployee?: Employee; // Optional reference to the full employee
  deliveryAddress?: string;
  deliveryInstructions?: string;
  notes?: string;
}

export interface OrderFilter {
  startDate?: Date;
  endDate?: Date;
  status?: OrderStatus[];
  orderType?: OrderType[];
  paymentStatus?: PaymentStatus[];
  searchQuery?: string;
}
