import EmployeeList from '@/components/employees/EmployeeList';
import EmployeeForm from '@/components/employees/EmployeeForm';
import { Employee } from '@/types/employee';
import { useState } from 'react';
import { useEmployeesStore, useRolesStore } from '@/store/store';

export function EmployeesPage() {
  const [isEmployeeFormOpen, setIsEmployeeFormOpen] = useState(false);
  const [currentEmployee, setCurrentEmployee] = useState<Employee | undefined>(
    undefined
  );

  const { employees, deleteEmployee, addEmployee } = useEmployeesStore();
  const { roles } = useRolesStore();

  const handleAddEmployee = () => {
    setCurrentEmployee(undefined);
    setIsEmployeeFormOpen(true);
  };

  const handleEditEmployee = (employee: Employee) => {
    setCurrentEmployee(employee);
    setIsEmployeeFormOpen(true);
  };

  const handleSaveEmployee = (employee: Employee) => {
    addEmployee(employee);
  };

  const handleDeleteEmployee = (employeeId: string) => {
    deleteEmployee(employeeId);
  };
  return (
    <div className='space-y-6'>
      <h1 className='text-3xl font-bold'>Employee Management</h1>
      <p className='text-muted-foreground'>
        View and manage your restaurant staff members.
      </p>

      <EmployeeList
        employees={employees}
        onAddEmployee={handleAddEmployee}
        onEditEmployee={handleEditEmployee}
        onDeleteEmployee={handleDeleteEmployee}
      />

      {isEmployeeFormOpen && (
        <EmployeeForm
          isOpen={isEmployeeFormOpen}
          onClose={() => setIsEmployeeFormOpen(false)}
          onSave={handleSaveEmployee}
          employee={currentEmployee}
          roles={roles}
        />
      )}
    </div>
  );
}

export default EmployeesPage;
