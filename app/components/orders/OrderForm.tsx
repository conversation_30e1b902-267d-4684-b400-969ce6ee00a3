import { useState, useEffect } from 'react';
import {
  Order,
  OrderItem,
  OrderStatus,
  OrderType,
  PaymentMethod,
  PaymentStatus,
} from '@/types/order';
import { Employee } from '@/types/employee';
import { MenuItem } from '@/types/menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Plus, Minus, Trash2 } from 'lucide-react';
import { useTablesStore } from '@/store/store'; // Import useTablesStore
import { Table as TableType } from '@/types/table'; // Import Table type

interface OrderFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (order: Order) => void;
  order?: Order;
  employees: Employee[];
  menuItems: MenuItem[];
  // tables prop removed, will be fetched from store
}

// Create a schema for form validation
const formSchema = z.object({
  orderNumber: z.string().min(1, 'Order number is required'),
  customerName: z.string().min(1, 'Customer name is required'),
  customerPhone: z.string().optional(),
  customerEmail: z.string().email().optional().or(z.literal('')),
  orderType: z.enum(['Dine-in', 'Takeout', 'Delivery', 'Catering']),
  tableNumber: z.string().optional(), // Keep for now, might be deprecated or used as display
  tableId: z.string().optional(),
  tableName: z.string().optional(),
  status: z.enum([
    'Pending',
    'In Progress',
    'Ready',
    'Delivered',
    'Completed',
    'Cancelled',
  ]),
  paymentStatus: z.enum(['Unpaid', 'Paid', 'Refunded', 'Partial']),
  paymentMethod: z
    .enum(['Cash', 'Credit Card', 'Debit Card', 'Mobile Payment', 'Gift Card'])
    .optional(),
  assignedTo: z.string(),
  deliveryAddress: z.string().optional(),
  deliveryInstructions: z.string().optional(),
  notes: z.string().optional(),
  discount: z.coerce.number().min(0).optional(),
  tip: z.coerce.number().min(0).optional(),
});

type FormValues = z.infer<typeof formSchema>;

const OrderForm = ({
  isOpen,
  onClose,
  onSave,
  order,
  employees,
  menuItems,
}: OrderFormProps) => {
  const { tables } = useTablesStore(); // Get tables from the store
  const isEditing = !!order;
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]); // Initialize empty, useEffect handles items and form reset
  const [activeTab, setActiveTab] = useState<string>('order-info');

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      orderNumber: '', // Initial empty or static default
      customerName: '',
      customerPhone: '',
      customerEmail: '',
      orderType: 'Dine-in',
      tableNumber: '',
      tableId: '',
      tableName: '',
      status: 'Pending',
      paymentStatus: 'Unpaid',
      paymentMethod: undefined, // Important for optional selects
      assignedTo: 'unassigned',
      deliveryAddress: '',
      deliveryInstructions: '',
      notes: '',
      discount: 0,
      tip: 0,
    },
  });

  // Update order items and form values when order changes or for a new form
  useEffect(() => {
    if (order) {
      setOrderItems(order.items || []);
      form.reset({
        orderNumber: order.orderNumber || `ORD-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
        customerName: order.customerName || '',
        customerPhone: order.customerPhone || '',
        customerEmail: order.customerEmail || '',
        orderType: order.orderType || 'Dine-in',
        tableNumber: order.tableNumber || '',
        tableId: order.tableId || '',
        tableName: order.tableName || '',
        status: order.status || 'Pending',
        paymentStatus: order.paymentStatus || 'Unpaid',
        paymentMethod: order.paymentMethod,
        assignedTo: order.assignedTo || 'unassigned',
        deliveryAddress: order.deliveryAddress || '',
        deliveryInstructions: order.deliveryInstructions || '',
        notes: order.notes || '',
        discount: order.discount || 0,
        tip: order.tip || 0,
      });
    } else {
      setOrderItems([]);
      // Reset form for new order
      form.reset({
        orderNumber: `ORD-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
        customerName: '',
        customerPhone: '',
        customerEmail: '',
        orderType: 'Dine-in',
        tableNumber: '',
        tableId: '',
        tableName: '',
        status: 'Pending',
        paymentStatus: 'Unpaid',
        paymentMethod: undefined,
        assignedTo: 'unassigned',
        deliveryAddress: '',
        deliveryInstructions: '',
        notes: '',
        discount: 0,
        tip: 0,
      });
    }
  }, [order, form]); // form added to dependency array

  // Calculate order totals
  const calculateOrderTotals = (
    items: OrderItem[],
    discount?: number,
    tip?: number
  ) => {
    const subtotal = items.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0
    );
    const tax = subtotal * 0.0825; // 8.25% tax rate
    let total = subtotal + tax;

    if (discount) {
      total -= discount;
    }

    if (tip) {
      total += tip;
    }

    return { subtotal, tax, total };
  };

  const { subtotal, tax, total } = calculateOrderTotals(
    orderItems,
    // Use form.getValues() for discount and tip in calculateOrderTotals
    // to ensure it uses the latest values from the form state,
    // especially if they are modified directly in the form.
    form.getValues('discount') || 0, 
    form.getValues('tip') || 0
  );

  // Watch for order type changes to show/hide relevant fields
  const orderType = form.watch('orderType');
  const paymentStatus = form.watch('paymentStatus');

  // Add a new order item
  const handleAddOrderItem = () => {
    if (menuItems.length === 0) return;

    const newItem: OrderItem = {
      id: crypto.randomUUID(),
      menuItemId: menuItems[0].id,
      quantity: 1,
      price: menuItems[0].price,
      modifiers: [],
    };

    setOrderItems([...orderItems, newItem]);
  };

  // Update an order item
  const handleUpdateOrderItem = (
    id: string,
    field: keyof OrderItem,
    value: any
  ) => {
    const updatedItems = orderItems.map((item) => {
      if (item.id === id) {
        if (field === 'menuItemId') {
          // Update price when menu item changes
          const menuItem = menuItems.find((m) => m.id === value);
          return {
            ...item,
            [field]: value,
            price: menuItem ? menuItem.price : item.price,
          };
        }
        return { ...item, [field]: value };
      }
      return item;
    });

    setOrderItems(updatedItems);
  };

  // Remove an order item
  const handleRemoveOrderItem = (id: string) => {
    setOrderItems(orderItems.filter((item) => item.id !== id));
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Get menu item name by ID
  const getMenuItemName = (menuItemId: string) => {
    const menuItem = menuItems.find((item) => item.id === menuItemId);
    return menuItem ? menuItem.name : 'Unknown Item';
  };

  const handleSubmit = (values: FormValues) => {
    if (orderItems.length === 0) {
      alert('Please add at least one item to the order');
      return;
    }

    const { subtotal, tax, total } = calculateOrderTotals(
      orderItems,
      values.discount,
      values.tip
    );

    const orderData: Order = {
      id: order?.id || crypto.randomUUID(),
      orderNumber: values.orderNumber,
      customerName: values.customerName,
      customerPhone: values.customerPhone,
      customerEmail: values.customerEmail || undefined,
      orderType: values.orderType as OrderType,
      tableNumber: values.tableNumber, // Keep for display or manual override if desired
      tableId: values.tableId,
      tableName: values.tableName,
      status: values.status as OrderStatus,
      items: orderItems,
      subtotal,
      tax,
      tip: values.tip,
      discount: values.discount,
      total,
      paymentStatus: values.paymentStatus as PaymentStatus,
      paymentMethod: values.paymentMethod as PaymentMethod | undefined,
      createdAt: order?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      completedAt: order?.completedAt,
      assignedTo:
        values.assignedTo === 'unassigned' ? undefined : values.assignedTo,
      deliveryAddress: values.deliveryAddress,
      deliveryInstructions: values.deliveryInstructions,
      notes: values.notes,
    };

    onSave(orderData);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className='sm:max-w-[800px] max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Order' : 'Create New Order'}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update order information.'
              : 'Fill in the details to create a new order.'}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className='grid w-full grid-cols-2'>
            <TabsTrigger value='order-info' className='cursor-pointer'>
              Order Information
            </TabsTrigger>
            <TabsTrigger value='order-items' className='cursor-pointer'>
              Order Items
            </TabsTrigger>
          </TabsList>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSubmit)}
              className='space-y-6'
            >
              <TabsContent value='order-info' className='space-y-6 pt-4'>
                <div className='grid grid-cols-2 gap-4'>
                  <FormField
                    control={form.control}
                    name='orderNumber'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Order Number</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='orderType'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Order Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder='Select order type' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value='Dine-in'>Dine-in</SelectItem>
                            <SelectItem value='Takeout'>Takeout</SelectItem>
                            <SelectItem value='Delivery'>Delivery</SelectItem>
                            <SelectItem value='Catering'>Catering</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {orderType === 'Dine-in' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> {/* Adjusted grid for responsiveness */}
                    <FormField
                      control={form.control}
                      name="tableId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Assign Table</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value);
                              const selectedTable = tables.find(t => t.id === value);
                              form.setValue('tableName', selectedTable?.name || '');
                              // If you want to auto-fill tableNumber input when a table is selected:
                              // form.setValue('tableNumber', selectedTable?.name || '');
                            }}
                            value={field.value || ''} // Ensure value is controlled
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a table" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="none">No Table</SelectItem>
                              {tables
                                .filter(t => t.status === 'available' || (isEditing && order?.tableId === t.id))
                                .map((table) => (
                                <SelectItem key={table.id} value={table.id}>
                                  {table.name} (Cap: {table.capacity}, Status: {table.status})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {/* Display table name if a tableId is selected, or allow manual input in tableNumber */}
                    <FormField
                      control={form.control}
                      name="tableNumber" 
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Table Number/Name (Display/Manual)</FormLabel>
                          <FormControl>
                            <Input 
                              {...field} 
                              placeholder={form.getValues('tableName') || "e.g., T1 or 12"}
                              // Consider making this readOnly if tableName is set and you don't want manual override
                              // readOnly={!!form.getValues('tableName')} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}

                <div className='space-y-4'>
                  <h3 className='text-sm font-medium'>Customer Information</h3>
                  <div className='grid grid-cols-2 gap-4'>
                    <FormField
                      control={form.control}
                      name='customerName'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Customer Name</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name='customerPhone'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone Number</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name='customerEmail'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {(orderType === 'Delivery' || orderType === 'Catering') && (
                  <div className='space-y-4'>
                    <h3 className='text-sm font-medium'>
                      Delivery Information
                    </h3>
                    <FormField
                      control={form.control}
                      name='deliveryAddress'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Delivery Address</FormLabel>
                          <FormControl>
                            <Textarea className='resize-none' {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name='deliveryInstructions'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Delivery Instructions</FormLabel>
                          <FormControl>
                            <Textarea className='resize-none' {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}

                <div className='space-y-4'>
                  <h3 className='text-sm font-medium'>Order Status</h3>
                  <div className='grid grid-cols-2 gap-4'>
                    <FormField
                      control={form.control}
                      name='status'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Status</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder='Select status' />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value='Pending'>Pending</SelectItem>
                              <SelectItem value='In Progress'>
                                In Progress
                              </SelectItem>
                              <SelectItem value='Ready'>Ready</SelectItem>
                              <SelectItem value='Delivered'>
                                Delivered
                              </SelectItem>
                              <SelectItem value='Completed'>
                                Completed
                              </SelectItem>
                              <SelectItem value='Cancelled'>
                                Cancelled
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name='assignedTo'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Assigned To</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder='Select employee' />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value='unassigned'>
                                Unassigned
                              </SelectItem>
                              {employees.map((employee) => (
                                <SelectItem
                                  key={employee.id}
                                  value={employee.id}
                                >
                                  {employee.name} ({employee.role.name})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className='space-y-4'>
                  <h3 className='text-sm font-medium'>Payment Information</h3>
                  <div className='grid grid-cols-2 gap-4'>
                    <FormField
                      control={form.control}
                      name='paymentStatus'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Payment Status</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder='Select payment status' />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value='Unpaid'>Unpaid</SelectItem>
                              <SelectItem value='Paid'>Paid</SelectItem>
                              <SelectItem value='Partial'>Partial</SelectItem>
                              <SelectItem value='Refunded'>Refunded</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {paymentStatus !== 'Unpaid' && (
                      <FormField
                        control={form.control}
                        name='paymentMethod'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Payment Method</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              value={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder='Select payment method' />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value='Cash'>Cash</SelectItem>
                                <SelectItem value='Credit Card'>
                                  Credit Card
                                </SelectItem>
                                <SelectItem value='Debit Card'>
                                  Debit Card
                                </SelectItem>
                                <SelectItem value='Mobile Payment'>
                                  Mobile Payment
                                </SelectItem>
                                <SelectItem value='Gift Card'>
                                  Gift Card
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>

                  <div className='grid grid-cols-2 gap-4'>
                    <FormField
                      control={form.control}
                      name='tip'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tip Amount</FormLabel>
                          <FormControl>
                            <Input
                              type='number'
                              min='0'
                              step='0.01'
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name='discount'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Discount Amount</FormLabel>
                          <FormControl>
                            <Input
                              type='number'
                              min='0'
                              step='0.01'
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name='notes'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Order Notes</FormLabel>
                      <FormControl>
                        <Textarea className='resize-none' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              <TabsContent value='order-items' className='space-y-6 pt-4'>
                <div className='flex justify-between items-center'>
                  <h3 className='text-sm font-medium'>Order Items</h3>
                  <Button
                    type='button'
                    variant='outline'
                    size='sm'
                    onClick={handleAddOrderItem}
                    className='cursor-pointer'
                  >
                    <Plus className='h-4 w-4 mr-2' />
                    Add Item
                  </Button>
                </div>

                {orderItems.length === 0 ? (
                  <div className='text-center p-4 border rounded-md text-muted-foreground'>
                    No items added to this order yet. Click "Add Item" to add
                    menu items.
                  </div>
                ) : (
                  <div className='space-y-4'>
                    {orderItems.map((item) => (
                      <Card key={item.id}>
                        <CardHeader className='py-3 px-4'>
                          <div className='flex justify-between items-center'>
                            <CardTitle className='text-sm font-medium'>
                              {getMenuItemName(item.menuItemId)}
                            </CardTitle>
                            <Button
                              type='button'
                              variant='ghost'
                              size='icon'
                              onClick={() => handleRemoveOrderItem(item.id)}
                              className='h-8 w-8 cursor-pointer'
                            >
                              <Trash2 className='h-4 w-4 text-destructive' />
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent className='py-2 px-4 space-y-4'>
                          <div className='grid grid-cols-2 gap-4'>
                            <div className='space-y-2'>
                              <FormLabel>Menu Item</FormLabel>
                              <Select
                                value={item.menuItemId}
                                onValueChange={(value) =>
                                  handleUpdateOrderItem(
                                    item.id,
                                    'menuItemId',
                                    value
                                  )
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select menu item' />
                                </SelectTrigger>
                                <SelectContent>
                                  {menuItems.map((menuItem) => (
                                    <SelectItem
                                      key={menuItem.id}
                                      value={menuItem.id}
                                    >
                                      {menuItem.name} -{' '}
                                      {formatCurrency(menuItem.price)}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>

                            <div className='space-y-2'>
                              <FormLabel>Price</FormLabel>
                              <Input
                                type='number'
                                min='0'
                                step='0.01'
                                value={item.price}
                                onChange={(e) =>
                                  handleUpdateOrderItem(
                                    item.id,
                                    'price',
                                    parseFloat(e.target.value)
                                  )
                                }
                              />
                            </div>
                          </div>

                          <div className='flex items-center gap-4'>
                            <div className='space-y-2 flex-1'>
                              <FormLabel>Quantity</FormLabel>
                              <div className='flex items-center'>
                                <Button
                                  type='button'
                                  variant='outline'
                                  size='icon'
                                  onClick={() => {
                                    if (item.quantity > 1) {
                                      handleUpdateOrderItem(
                                        item.id,
                                        'quantity',
                                        item.quantity - 1
                                      );
                                    }
                                  }}
                                  className='h-8 w-8 cursor-pointer'
                                >
                                  <Minus className='h-3 w-3' />
                                </Button>
                                <Input
                                  type='number'
                                  min='1'
                                  value={item.quantity}
                                  onChange={(e) =>
                                    handleUpdateOrderItem(
                                      item.id,
                                      'quantity',
                                      parseInt(e.target.value)
                                    )
                                  }
                                  className='h-8 mx-2 text-center'
                                />
                                <Button
                                  type='button'
                                  variant='outline'
                                  size='icon'
                                  onClick={() =>
                                    handleUpdateOrderItem(
                                      item.id,
                                      'quantity',
                                      item.quantity + 1
                                    )
                                  }
                                  className='h-8 w-8 cursor-pointer'
                                >
                                  <Plus className='h-3 w-3' />
                                </Button>
                              </div>
                            </div>

                            <div className='space-y-2 flex-1'>
                              <FormLabel>Total</FormLabel>
                              <div className='h-8 flex items-center px-3 border rounded-md bg-muted'>
                                {formatCurrency(item.price * item.quantity)}
                              </div>
                            </div>
                          </div>

                          <div className='space-y-2'>
                            <FormLabel>Special Instructions</FormLabel>
                            <Textarea
                              placeholder='Any special requests or modifications'
                              className='resize-none'
                              value={item.notes || ''}
                              onChange={(e) =>
                                handleUpdateOrderItem(
                                  item.id,
                                  'notes',
                                  e.target.value
                                )
                              }
                            />
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}

                <Separator />

                <div className='space-y-2'>
                  <div className='flex justify-between'>
                    <span>Subtotal</span>
                    <span>{formatCurrency(subtotal)}</span>
                  </div>
                  <div className='flex justify-between'>
                    <span>Tax (8.25%)</span>
                    <span>{formatCurrency(tax)}</span>
                  </div>
                  {(form.getValues('tip') || 0) > 0 && (
                    <div className='flex justify-between'>
                      <span>Tip</span>
                      <span>{formatCurrency(form.getValues('tip') || 0)}</span>
                    </div>
                  )}
                  {(form.getValues('discount') || 0) > 0 && (
                    <div className='flex justify-between'>
                      <span>Discount</span>
                      <span>
                        -{formatCurrency(form.getValues('discount') || 0)}
                      </span>
                    </div>
                  )}
                  <Separator />
                  <div className='flex justify-between font-bold'>
                    <span>Total</span>
                    <span>{formatCurrency(total)}</span>
                  </div>
                </div>
              </TabsContent>

              <DialogFooter>
                <Button type='button' variant='outline' onClick={onClose}>
                  Cancel
                </Button>
                <Button type='submit'>
                  {isEditing ? 'Update Order' : 'Create Order'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default OrderForm;
