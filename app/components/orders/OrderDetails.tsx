import { Order, OrderStatus, PaymentStatus } from '@/types/order';
import { Employee } from '@/types/employee';
import { MenuItem } from '@/types/menu';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { format, parseISO } from 'date-fns';
import { X, Edit, Clock, MapPin, Phone, Mail, User, CreditCard, FileText } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface OrderDetailsProps {
  order: Order;
  employees: Employee[];
  menuItems: MenuItem[];
  onClose: () => void;
  onEdit: () => void;
  onUpdateStatus: (orderId: string, status: OrderStatus) => void;
  onUpdatePaymentStatus: (orderId: string, status: PaymentStatus) => void;
}

const OrderDetails = ({
  order,
  employees,
  menuItems,
  onClose,
  onEdit,
  onUpdateStatus,
  onUpdatePaymentStatus,
}: OrderDetailsProps) => {
  // Format date and time
  const formatDateTime = (isoString: string) => {
    return format(parseISO(isoString), 'MMM d, yyyy h:mm a');
  };

  // Get employee name by ID
  const getEmployeeName = (employeeId?: string) => {
    if (!employeeId) return 'Unassigned';
    const employee = employees.find((e) => e.id === employeeId);
    return employee ? employee.name : 'Unknown';
  };

  // Get menu item name by ID
  const getMenuItemName = (menuItemId: string) => {
    const menuItem = menuItems.find((item) => item.id === menuItemId);
    return menuItem ? menuItem.name : 'Unknown Item';
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: OrderStatus) => {
    switch (status) {
      case 'Pending':
        return 'secondary';
      case 'In Progress':
        return 'default';
      case 'Ready':
        return 'outline';
      case 'Delivered':
        return 'default';
      case 'Completed':
        return 'default';
      case 'Cancelled':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  // Get payment status badge variant
  const getPaymentStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'Paid':
        return 'default';
      case 'Unpaid':
        return 'secondary';
      case 'Refunded':
        return 'destructive';
      case 'Partial':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-xl'>Order #{order.orderNumber}</CardTitle>
        <div className='flex gap-2'>
          <Button
            variant='ghost'
            size='icon'
            onClick={onEdit}
            className='cursor-pointer'
          >
            <Edit className='h-4 w-4' />
            <span className='sr-only'>Edit</span>
          </Button>
          <Button
            variant='ghost'
            size='icon'
            onClick={onClose}
            className='cursor-pointer'
          >
            <X className='h-4 w-4' />
            <span className='sr-only'>Close</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='flex justify-between items-center'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-1 text-sm text-muted-foreground'>
              <Clock className='h-3 w-3' />
              <span>{formatDateTime(order.createdAt)}</span>
            </div>
            <div className='flex items-center gap-1 mt-1'>
              <Badge variant={getStatusBadgeVariant(order.status) as any}>
                {order.status}
              </Badge>
              <Badge variant={getPaymentStatusBadgeVariant(order.paymentStatus) as any}>
                {order.paymentStatus}
              </Badge>
            </div>
          </div>
          
          <div className='flex flex-col items-end'>
            <span className='text-sm text-muted-foreground'>Order Type</span>
            <span className='font-medium'>{order.orderType}</span>
            {(order.tableName || order.tableId || order.tableNumber) && (
              <span className='text-sm'>
                Table: {order.tableName || order.tableId || order.tableNumber}
              </span>
            )}
          </div>
        </div>

        <Separator />

        <div className='space-y-2'>
          <h3 className='font-medium'>Customer Information</h3>
          <div className='grid grid-cols-1 gap-2'>
            <div className='flex items-center gap-2'>
              <User className='h-4 w-4 text-muted-foreground' />
              <span>{order.customerName}</span>
            </div>
            {order.customerPhone && (
              <div className='flex items-center gap-2'>
                <Phone className='h-4 w-4 text-muted-foreground' />
                <span>{order.customerPhone}</span>
              </div>
            )}
            {order.customerEmail && (
              <div className='flex items-center gap-2'>
                <Mail className='h-4 w-4 text-muted-foreground' />
                <span>{order.customerEmail}</span>
              </div>
            )}
            {order.deliveryAddress && (
              <div className='flex items-start gap-2'>
                <MapPin className='h-4 w-4 text-muted-foreground mt-0.5' />
                <div className='flex flex-col'>
                  <span>{order.deliveryAddress}</span>
                  {order.deliveryInstructions && (
                    <span className='text-xs text-muted-foreground'>
                      {order.deliveryInstructions}
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        <Separator />

        <div className='space-y-2'>
          <h3 className='font-medium'>Order Items</h3>
          <div className='space-y-2'>
            {order.items.map((item) => (
              <div key={item.id} className='flex justify-between items-center'>
                <div className='flex items-center gap-2'>
                  <span className='font-medium'>{item.quantity}x</span>
                  <div className='flex flex-col'>
                    <span>{getMenuItemName(item.menuItemId)}</span>
                    {item.notes && (
                      <span className='text-xs text-muted-foreground'>
                        Note: {item.notes}
                      </span>
                    )}
                    {item.modifiers && item.modifiers.length > 0 && (
                      <span className='text-xs text-muted-foreground'>
                        {item.modifiers.join(', ')}
                      </span>
                    )}
                  </div>
                </div>
                <span>{formatCurrency(item.price * item.quantity)}</span>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        <div className='space-y-2'>
          <div className='flex justify-between'>
            <span>Subtotal</span>
            <span>{formatCurrency(order.subtotal)}</span>
          </div>
          <div className='flex justify-between'>
            <span>Tax</span>
            <span>{formatCurrency(order.tax)}</span>
          </div>
          {order.tip !== undefined && (
            <div className='flex justify-between'>
              <span>Tip</span>
              <span>{formatCurrency(order.tip)}</span>
            </div>
          )}
          {order.discount !== undefined && (
            <div className='flex justify-between'>
              <span>Discount</span>
              <span>-{formatCurrency(order.discount)}</span>
            </div>
          )}
          <div className='flex justify-between font-bold'>
            <span>Total</span>
            <span>{formatCurrency(order.total)}</span>
          </div>
        </div>

        {order.paymentMethod && (
          <>
            <Separator />
            <div className='flex items-center gap-2'>
              <CreditCard className='h-4 w-4 text-muted-foreground' />
              <span>Paid with {order.paymentMethod}</span>
            </div>
          </>
        )}

        {order.notes && (
          <>
            <Separator />
            <div className='flex items-start gap-2'>
              <FileText className='h-4 w-4 text-muted-foreground mt-0.5' />
              <div className='flex flex-col'>
                <span className='font-medium'>Notes</span>
                <span className='text-sm'>{order.notes}</span>
              </div>
            </div>
          </>
        )}

        <Separator />

        <div className='flex items-center gap-2'>
          <span className='text-sm text-muted-foreground'>Assigned to:</span>
          <span>{getEmployeeName(order.assignedTo)}</span>
        </div>
      </CardContent>
      <CardFooter className='flex justify-between'>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='outline' className='cursor-pointer'>
              Update Status
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Order Status</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={() => onUpdateStatus(order.id, 'Pending')}
              className='cursor-pointer hover:bg-accent'
            >
              Pending
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => onUpdateStatus(order.id, 'In Progress')}
              className='cursor-pointer hover:bg-accent'
            >
              In Progress
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => onUpdateStatus(order.id, 'Ready')}
              className='cursor-pointer hover:bg-accent'
            >
              Ready
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => onUpdateStatus(order.id, 'Delivered')}
              className='cursor-pointer hover:bg-accent'
            >
              Delivered
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => onUpdateStatus(order.id, 'Completed')}
              className='cursor-pointer hover:bg-accent'
            >
              Completed
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => onUpdateStatus(order.id, 'Cancelled')}
              className='cursor-pointer hover:bg-accent text-destructive'
            >
              Cancelled
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='outline' className='cursor-pointer'>
              Update Payment
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Payment Status</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={() => onUpdatePaymentStatus(order.id, 'Unpaid')}
              className='cursor-pointer hover:bg-accent'
            >
              Unpaid
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => onUpdatePaymentStatus(order.id, 'Paid')}
              className='cursor-pointer hover:bg-accent'
            >
              Paid
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => onUpdatePaymentStatus(order.id, 'Partial')}
              className='cursor-pointer hover:bg-accent'
            >
              Partial
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => onUpdatePaymentStatus(order.id, 'Refunded')}
              className='cursor-pointer hover:bg-accent text-destructive'
            >
              Refunded
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardFooter>
    </Card>
  );
};

export default OrderDetails;
