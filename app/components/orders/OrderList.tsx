import { useState } from 'react';
import { Order, OrderStatus } from '@/types/order';
import { toast } from 'sonner';
import { DeleteConfirmation } from '@/components/ui/delete-confirmation';
import { Employee } from '@/types/employee';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreVertical, Pencil, Trash2, Eye, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { format, parseISO } from 'date-fns';
import { cn } from '@/lib/utils';

interface OrderListProps {
  orders: Order[];
  employees: Employee[];
  onViewOrder: (order: Order) => void;
  onEditOrder: (order: Order) => void;
  onDeleteOrder: (orderId: string) => void;
  onUpdateOrderStatus: (orderId: string, status: OrderStatus) => void;
  selectedOrderId?: string;
}

const OrderList = ({
  orders,
  employees,
  onViewOrder,
  onEditOrder,
  onDeleteOrder,
  onUpdateOrderStatus,
  selectedOrderId,
}: OrderListProps) => {
  const [orderToDelete, setOrderToDelete] = useState<Order | null>(null);
  // Format date and time
  const formatDateTime = (isoString: string) => {
    return format(parseISO(isoString), 'MMM d, yyyy h:mm a');
  };

  // Get employee name by ID
  const getEmployeeName = (employeeId?: string) => {
    if (!employeeId) return 'Unassigned';
    const employee = employees.find((e) => e.id === employeeId);
    return employee ? employee.name : 'Unknown';
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: OrderStatus) => {
    switch (status) {
      case 'Pending':
        return 'secondary';
      case 'In Progress':
        return 'default';
      case 'Ready':
        return 'outline';
      case 'Delivered':
        return 'default';
      case 'Completed':
        return 'default';
      case 'Cancelled':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  // Get payment status badge variant
  const getPaymentStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'Paid':
        return 'default';
      case 'Unpaid':
        return 'secondary';
      case 'Refunded':
        return 'destructive';
      case 'Partial':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Order #</TableHead>
            <TableHead>Customer</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Table</TableHead> {/* New Table column header */}
            <TableHead>Date</TableHead>
            <TableHead>Total</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Payment</TableHead>
            <TableHead className='text-right'>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {orders.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={9} // Adjusted colSpan
                className='text-center h-32 text-muted-foreground'
              >
                No orders found
              </TableCell>
            </TableRow>
          ) : (
            orders.map((order) => (
              <TableRow
                key={order.id}
                className={cn(
                  'cursor-pointer hover:bg-muted/50',
                  selectedOrderId === order.id && 'bg-muted'
                )}
                onClick={() => onViewOrder(order)}
              >
                <TableCell className='font-medium'>
                  {order.orderNumber}
                </TableCell>
                <TableCell>
                  <div className='flex flex-col'>
                    <span>{order.customerName}</span>
                    {order.tableNumber && (
                      <span className='text-xs text-muted-foreground'>
                        Table: {order.tableNumber}
                      </span>
                    )}
                  </div>
                </TableCell>
                <TableCell>{order.orderType}</TableCell>
                <TableCell>{order.tableName || order.tableId || 'N/A'}</TableCell> {/* Display table name or ID */}
                <TableCell>
                  <div className='flex items-center'>
                    <Clock className='h-3 w-3 mr-1 text-muted-foreground' />
                    <span className='text-xs'>
                      {formatDateTime(order.createdAt)}
                    </span>
                  </div>
                </TableCell>
                <TableCell>{formatCurrency(order.total)}</TableCell>
                <TableCell>
                  <Badge variant={getStatusBadgeVariant(order.status) as any}>
                    {order.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge
                    variant={
                      getPaymentStatusBadgeVariant(order.paymentStatus) as any
                    }
                  >
                    {order.paymentStatus}
                  </Badge>
                </TableCell>
                <TableCell className='text-right'>
                  <DropdownMenu>
                    <DropdownMenuTrigger
                      asChild
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Button
                        variant='ghost'
                        size='icon'
                        className='cursor-pointer'
                      >
                        <MoreVertical className='h-4 w-4' />
                        <span className='sr-only'>Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align='end' className='bg-popover'>
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          onViewOrder(order);
                        }}
                        className='cursor-pointer hover:bg-accent'
                      >
                        <Eye className='mr-2 h-4 w-4' />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          onEditOrder(order);
                        }}
                        className='cursor-pointer hover:bg-accent'
                      >
                        <Pencil className='mr-2 h-4 w-4' />
                        Edit
                      </DropdownMenuItem>

                      <DropdownMenuSub>
                        <DropdownMenuSubTrigger className='cursor-pointer'>
                          Update Status
                        </DropdownMenuSubTrigger>
                        <DropdownMenuSubContent>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              onUpdateOrderStatus(order.id, 'Pending');
                            }}
                            className='cursor-pointer hover:bg-accent'
                          >
                            Pending
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              onUpdateOrderStatus(order.id, 'In Progress');
                            }}
                            className='cursor-pointer hover:bg-accent'
                          >
                            In Progress
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              onUpdateOrderStatus(order.id, 'Ready');
                            }}
                            className='cursor-pointer hover:bg-accent'
                          >
                            Ready
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              onUpdateOrderStatus(order.id, 'Delivered');
                            }}
                            className='cursor-pointer hover:bg-accent'
                          >
                            Delivered
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              onUpdateOrderStatus(order.id, 'Completed');
                            }}
                            className='cursor-pointer hover:bg-accent'
                          >
                            Completed
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              onUpdateOrderStatus(order.id, 'Cancelled');
                            }}
                            className='cursor-pointer hover:bg-accent text-destructive'
                          >
                            Cancelled
                          </DropdownMenuItem>
                        </DropdownMenuSubContent>
                      </DropdownMenuSub>

                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          setOrderToDelete(order);
                        }}
                        className='text-destructive focus:text-destructive cursor-pointer hover:bg-accent'
                      >
                        <Trash2 className='mr-2 h-4 w-4' />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmation
        isOpen={!!orderToDelete}
        onClose={() => setOrderToDelete(null)}
        onConfirm={() => {
          if (orderToDelete) {
            onDeleteOrder(orderToDelete.id);
            toast.success(
              `Order #${orderToDelete.orderNumber} has been deleted.`
            );
            setOrderToDelete(null);
          }
        }}
        title='Delete Order'
        description={`Are you sure you want to delete Order #${orderToDelete?.orderNumber}? This action cannot be undone.`}
      />
    </div>
  );
};

export default OrderList;
