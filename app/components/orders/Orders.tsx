import { useState } from 'react';
import { Order, OrderStatus, OrderType, PaymentStatus } from '@/types/order';
import { useOrdersStore, useEmployeesStore, useMenuItemsStore } from '@/store/store';
import OrderList from '@/components/orders/OrderList';
import OrderDetails from '@/components/orders/OrderDetails';
import OrderForm from '@/components/orders/OrderForm';
import { Button } from '@/components/ui/button';
import { Plus, Filter, Search } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { subDays } from 'date-fns';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { DateRange } from 'react-day-picker';

const OrdersPage = () => {
  const { orders, addOrder, deleteOrder, updateOrder, updateOrderStatus, updatePaymentStatus } = useOrdersStore();
  const { employees } = useEmployeesStore();
  const { menuItems } = useMenuItemsStore();
  
  const [isOrderFormOpen, setIsOrderFormOpen] = useState(false);
  const [currentOrder, setCurrentOrder] = useState<Order | undefined>(undefined);
  const [selectedOrder, setSelectedOrder] = useState<Order | undefined>(undefined);
  
  // Filters
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<OrderStatus | 'all'>('all');
  const [orderTypeFilter, setOrderTypeFilter] = useState<OrderType | 'all'>('all');
  const [paymentStatusFilter, setPaymentStatusFilter] = useState<PaymentStatus | 'all'>('all');
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: subDays(new Date(), 7),
    to: new Date(),
  });

  // Order handlers
  const handleAddOrder = () => {
    setCurrentOrder(undefined);
    setIsOrderFormOpen(true);
  };

  const handleEditOrder = (order: Order) => {
    setCurrentOrder(order);
    setIsOrderFormOpen(true);
  };

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
  };

  const handleCloseOrderDetails = () => {
    setSelectedOrder(undefined);
  };

  const handleSaveOrder = (order: Order) => {
    const index = orders.findIndex((o) => o.id === order.id);
    if (index >= 0) {
      updateOrder(order.id, order);
    } else {
      addOrder(order);
    }
  };

  const handleDeleteOrder = (orderId: string) => {
    if (selectedOrder?.id === orderId) {
      setSelectedOrder(undefined);
    }
    deleteOrder(orderId);
  };

  const handleUpdateOrderStatus = (orderId: string, status: OrderStatus) => {
    updateOrderStatus(orderId, status);
    if (selectedOrder?.id === orderId) {
      const updatedOrder = orders.find(o => o.id === orderId);
      if (updatedOrder) {
        setSelectedOrder({
          ...updatedOrder,
          status,
          updatedAt: new Date().toISOString()
        });
      }
    }
  };

  const handleUpdatePaymentStatus = (orderId: string, paymentStatus: PaymentStatus) => {
    updatePaymentStatus(orderId, paymentStatus);
    if (selectedOrder?.id === orderId) {
      const updatedOrder = orders.find(o => o.id === orderId);
      if (updatedOrder) {
        setSelectedOrder({
          ...updatedOrder,
          paymentStatus,
          updatedAt: new Date().toISOString()
        });
      }
    }
  };

  // Filter orders
  const filteredOrders = orders.filter((order) => {
    // Search filter
    const matchesSearch =
      searchQuery === '' ||
      order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (order.customerPhone && order.customerPhone.includes(searchQuery)) ||
      (order.customerEmail && order.customerEmail.toLowerCase().includes(searchQuery.toLowerCase()));

    // Status filter
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;

    // Order type filter
    const matchesOrderType = orderTypeFilter === 'all' || order.orderType === orderTypeFilter;

    // Payment status filter
    const matchesPaymentStatus = paymentStatusFilter === 'all' || order.paymentStatus === paymentStatusFilter;

    // Date range filter
    const orderDate = new Date(order.createdAt);
    const matchesDateRange =
      !dateRange ||
      !dateRange.from ||
      (orderDate >= dateRange.from &&
        (!dateRange.to || orderDate <= dateRange.to));

    return matchesSearch && matchesStatus && matchesOrderType && matchesPaymentStatus && matchesDateRange;
  });

  // Sort orders by creation date (newest first)
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  return (
    <div className='space-y-6'>
      <h1 className='text-3xl font-bold'>Order Management</h1>
      <p className='text-muted-foreground'>
        Create and manage customer orders.
      </p>

      <div className='flex flex-col md:flex-row gap-4'>
        <div className='w-full md:w-2/3 space-y-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle>Orders</CardTitle>
              <Button onClick={handleAddOrder} className='cursor-pointer'>
                <Plus className='h-4 w-4 mr-2' />
                New Order
              </Button>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex flex-col sm:flex-row gap-4'>
                  <div className='relative flex-1'>
                    <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
                    <Input
                      type='search'
                      placeholder='Search orders...'
                      className='pl-8'
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  
                  <div className='flex items-center gap-2'>
                    <Filter className='h-4 w-4 text-muted-foreground' />
                    <span className='text-sm font-medium'>Filters:</span>
                  </div>
                </div>

                <div className='flex flex-wrap gap-4'>
                  <Select
                    value={statusFilter}
                    onValueChange={(value) => setStatusFilter(value as OrderStatus | 'all')}
                  >
                    <SelectTrigger className='w-[150px]'>
                      <SelectValue placeholder='Status' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='all'>All Statuses</SelectItem>
                      <SelectItem value='Pending'>Pending</SelectItem>
                      <SelectItem value='In Progress'>In Progress</SelectItem>
                      <SelectItem value='Ready'>Ready</SelectItem>
                      <SelectItem value='Delivered'>Delivered</SelectItem>
                      <SelectItem value='Completed'>Completed</SelectItem>
                      <SelectItem value='Cancelled'>Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select
                    value={orderTypeFilter}
                    onValueChange={(value) => setOrderTypeFilter(value as OrderType | 'all')}
                  >
                    <SelectTrigger className='w-[150px]'>
                      <SelectValue placeholder='Order Type' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='all'>All Types</SelectItem>
                      <SelectItem value='Dine-in'>Dine-in</SelectItem>
                      <SelectItem value='Takeout'>Takeout</SelectItem>
                      <SelectItem value='Delivery'>Delivery</SelectItem>
                      <SelectItem value='Catering'>Catering</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select
                    value={paymentStatusFilter}
                    onValueChange={(value) => setPaymentStatusFilter(value as PaymentStatus | 'all')}
                  >
                    <SelectTrigger className='w-[150px]'>
                      <SelectValue placeholder='Payment' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='all'>All Payments</SelectItem>
                      <SelectItem value='Unpaid'>Unpaid</SelectItem>
                      <SelectItem value='Paid'>Paid</SelectItem>
                      <SelectItem value='Refunded'>Refunded</SelectItem>
                      <SelectItem value='Partial'>Partial</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <DateRangePicker
                    value={dateRange}
                    onChange={setDateRange}
                    className='w-full sm:w-auto'
                  />
                </div>

                <OrderList
                  orders={sortedOrders}
                  employees={employees}
                  onViewOrder={handleViewOrder}
                  onEditOrder={handleEditOrder}
                  onDeleteOrder={handleDeleteOrder}
                  onUpdateOrderStatus={handleUpdateOrderStatus}
                  selectedOrderId={selectedOrder?.id}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className='w-full md:w-1/3'>
          {selectedOrder ? (
            <OrderDetails
              order={selectedOrder}
              employees={employees}
              menuItems={menuItems}
              onClose={handleCloseOrderDetails}
              onEdit={() => handleEditOrder(selectedOrder)}
              onUpdateStatus={handleUpdateOrderStatus}
              onUpdatePaymentStatus={handleUpdatePaymentStatus}
            />
          ) : (
            <Card>
              <CardContent className='p-6 text-center text-muted-foreground'>
                <p>Select an order to view details</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      <OrderForm
        isOpen={isOrderFormOpen}
        onClose={() => setIsOrderFormOpen(false)}
        onSave={handleSaveOrder}
        order={currentOrder}
        employees={employees}
        menuItems={menuItems}
      />
    </div>
  );
};

export default OrdersPage;
