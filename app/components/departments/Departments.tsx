import Layout from '@/components/layout/Layout';
import DepartmentList from '@/components/departments/DepartmentList';
import DepartmentForm from '@/components/departments/DepartmentForm';
import { Department } from '@/types/department';
import { useState } from 'react';
import { useDepartmentsStore } from '@/store/store';

const DepartmentsPage = () => {
  const { departments, addDepartment, updateDepartment, deleteDepartment } =
    useDepartmentsStore();
  const [isDepartmentFormOpen, setIsDepartmentFormOpen] = useState(false);
  const [currentDepartment, setCurrentDepartment] = useState<
    Department | undefined
  >(undefined);

  const handleAddDepartment = () => {
    setCurrentDepartment(undefined);
    setIsDepartmentFormOpen(true);
  };

  const handleEditDepartment = (department: Department) => {
    setCurrentDepartment(department);
    setIsDepartmentFormOpen(true);
  };

  const handleSaveDepartment = (department: Department) => {
    const index = departments.findIndex((d) => d.id === department.id);
    if (index >= 0) {
      updateDepartment(department.id, department);
    } else {
      addDepartment(department);
    }
  };

  const handleDeleteDepartment = (departmentId: string) => {
    // Check if department is used by any roles
    // const rolesWithDepartment = roles.filter(
    //   (role) => role.departmentId === departmentId
    // );

    // if (rolesWithDepartment.length > 0) {
    //   alert(
    //     `Cannot delete department: This department is used by ${rolesWithDepartment.length} role(s). Please reassign those roles first.`
    //   );
    //   return;
    // }

    deleteDepartment(departmentId);
  };

  return (
    <div className='space-y-6'>
      <h1 className='text-3xl font-bold'>Department Management</h1>
      <p className='text-muted-foreground'>
        Create and manage departments for your restaurant.
      </p>

      <DepartmentList
        departments={departments}
        onAddDepartment={handleAddDepartment}
        onEditDepartment={handleEditDepartment}
        onDeleteDepartment={handleDeleteDepartment}
      />

      {isDepartmentFormOpen && (
        <DepartmentForm
          isOpen={isDepartmentFormOpen}
          onClose={() => setIsDepartmentFormOpen(false)}
          onSave={handleSaveDepartment}
          department={currentDepartment}
        />
      )}
    </div>
  );
};

export default DepartmentsPage;
