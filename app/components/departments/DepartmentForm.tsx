import React, { useEffect, useState } from 'react';
import { Department } from '@/types/department';
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { useForm } from 'react-hook-form';

interface DepartmentFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (department: Department) => void;
  department?: Department;
}

const DepartmentForm = ({
  isOpen,
  onClose,
  onSave,
  department,
}: DepartmentFormProps) => {
  const isEditing = !!department;

  const form = useForm({
    defaultValues: {
      name: department?.name || '',
      description: department?.description || '',
    },
  });

  useEffect(() => {
    if (department) {
      form.reset({
        name: department.name,
        description: department.description,
      });
    } else {
      form.reset({
        name: '',
        description: '',
      });
    }
  }, [department, form]);

  const handleSubmit = (values: { name: string; description: string }) => {
    const departmentData: Department = {
      id: department?.id || crypto.randomUUID(),
      name: values.name,
      description: values.description,
    };

    onSave(departmentData);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className='sm:max-w-[500px]'>
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Department' : 'Add New Department'}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update department information.'
              : 'Fill in the details to add a new department.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-6'
          >
            <FormField
              control={form.control}
              name='name'
              rules={{
                required: 'Department name is required',
                minLength: {
                  value: 2,
                  message: 'Name must be at least 2 characters.',
                },
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Department Name</FormLabel>
                  <FormControl>
                    <Input placeholder='Kitchen' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Brief department description'
                      className='resize-none'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type='button' variant='outline' onClick={onClose}>
                Cancel
              </Button>
              <Button type='submit'>{isEditing ? 'Update' : 'Create'}</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default DepartmentForm;
