import { useState } from 'react';
import { toast } from 'sonner';
import { DeleteConfirmation } from '@/components/ui/delete-confirmation';
import { Department } from '@/types/department';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Building,
  Pencil,
  Trash2,
  MoreVertical,
  Plus,
  Search,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface DepartmentListProps {
  departments: Department[];
  onAddDepartment: () => void;
  onEditDepartment: (department: Department) => void;
  onDeleteDepartment: (departmentId: string) => void;
}

const DepartmentList = ({
  departments,
  onAddDepartment,
  onEditDepartment,
  onDeleteDepartment,
}: DepartmentListProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [departmentToDelete, setDepartmentToDelete] =
    useState<Department | null>(null);

  const filteredDepartments = departments.filter((department) =>
    department.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleDeleteClick = (department: Department) => {
    setDepartmentToDelete(department);
  };

  const confirmDelete = () => {
    if (departmentToDelete) {
      onDeleteDepartment(departmentToDelete.id);
      toast.success(
        `${departmentToDelete.name} department has been removed successfully.`
      );
      setDepartmentToDelete(null);
    }
  };

  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
        <CardTitle>Departments</CardTitle>
        <div className='flex items-center gap-2'>
          <div className='relative'>
            <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
            <Input
              type='search'
              placeholder='Search departments...'
              className='w-[250px] pl-8'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button onClick={onAddDepartment}>
            <Plus className='h-4 w-4' />
            New
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead className='text-right'>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredDepartments.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={6}
                  className='text-center h-32 text-muted-foreground'
                >
                  No departments found
                </TableCell>
              </TableRow>
            ) : (
              filteredDepartments.map((department) => (
                <TableRow key={department.id}>
                  <TableCell className='font-medium align-middle'>
                    <div className='flex items-center gap-2'>
                      <Building className='h-4 w-4 text-gray-500' />
                      {department.name}
                    </div>
                  </TableCell>
                  <TableCell className='align-middle'>
                    {department.description}
                  </TableCell>
                  <TableCell className='text-right'>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant='ghost' size='icon'>
                          <MoreVertical className='h-4 w-4' />
                          <span className='sr-only'>Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align='end' className='bg-popover'>
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => onEditDepartment(department)}
                        >
                          <Pencil className='mr-2 h-4 w-4' /> Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className='text-destructive focus:text-destructive'
                          onClick={() => handleDeleteClick(department)}
                        >
                          <Trash2 className='mr-2 h-4 w-4' /> Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmation
        isOpen={!!departmentToDelete}
        onClose={() => setDepartmentToDelete(null)}
        onConfirm={confirmDelete}
        title='Delete Department'
        description={`Are you sure you want to delete the "${departmentToDelete?.name}" department? This action cannot be undone.`}
      />
    </Card>
  );
};

export default DepartmentList;
