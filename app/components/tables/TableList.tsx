'use client';

import React, { useState } from 'react';
import { Table as TableType } from '@/types/table';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Edit, Trash2 } from 'lucide-react';
import { DeleteConfirmation } from '@/components/ui/delete-confirmation'; // Assuming this path is correct
import { useSpacesStore } from '@/store/store';

interface TableListProps {
  tables: TableType[];
  onEdit: (table: TableType) => void;
  onDelete: (id: string) => void;
}

const TableList: React.FC<TableListProps> = ({
  tables,
  onEdit,
  onDelete,
}) => {
  const { spaces } = useSpacesStore();
  const [deleteTarget, setDeleteTarget] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleDeleteClick = (id: string) => {
    setDeleteTarget(id);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (deleteTarget) {
      onDelete(deleteTarget);
    }
    setIsDeleteDialogOpen(false);
    setDeleteTarget(null);
  };

  const cancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setDeleteTarget(null);
  };

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Capacity</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Space</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tables.length > 0 ? (
              tables.map((table) => {
                const spaceName = table.spaceId ? (spaces.find(s => s.id === table.spaceId)?.name || 'Unknown') : 'N/A';
                return (
                  <TableRow key={table.id}>
                    <TableCell>{table.name}</TableCell>
                    <TableCell>{table.capacity}</TableCell>
                    <TableCell>
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium
                          ${
                            table.status === 'available'
                              ? 'bg-green-100 text-green-800'
                              : table.status === 'occupied'
                              ? 'bg-yellow-100 text-yellow-800'
                              : table.status === 'reserved'
                              ? 'bg-blue-100 text-blue-800'
                              : table.status === 'out-of-service'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                      >
                        {table.status
                          .replace('-', ' ')
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </span>
                    </TableCell>
                    <TableCell>{spaceName}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="outline"
                        size="icon"
                        className="mr-2"
                        onClick={() => onEdit(table)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="destructive"
                        size="icon"
                        onClick={() => handleDeleteClick(table.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="text-center">
                  No tables found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {isDeleteDialogOpen && (
        <DeleteConfirmation
          isOpen={isDeleteDialogOpen}
          onConfirm={confirmDelete}
          onClose={cancelDelete}
          description={`Are you sure you want to delete table "${tables.find((t) => t.id === deleteTarget)?.name || 'table'}"? This action cannot be undone.`}
        />
      )}
    </>
  );
};

export default TableList;
