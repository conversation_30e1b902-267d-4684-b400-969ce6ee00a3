'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Table } from '@/types/table';
import { useSpacesStore } from '@/store/store';

// Zod schema for table validation
const tableSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  capacity: z.coerce.number().int().positive('Capacity must be a positive number'),
  status: z.enum(['available', 'occupied', 'reserved', 'out-of-service'], {
    required_error: 'Status is required',
  }),
  spaceId: z.string().optional(),
});

type TableFormValues = z.infer<typeof tableSchema>;

interface TableFormProps {
  table?: Table;
  onSubmit: (data: Partial<TableFormValues>) => void; // Partial for update
  onCancel: () => void;
}

const TableForm: React.FC<TableFormProps> = ({
  table,
  onSubmit,
  onCancel,
}) => {
  const { spaces } = useSpacesStore();

  const form = useForm<TableFormValues>({
    resolver: zodResolver(tableSchema),
    defaultValues: {
      name: table?.name || '',
      capacity: table?.capacity || 1,
      status: table?.status || 'available',
      spaceId: table?.spaceId || '',
    },
  });

  useEffect(() => {
    if (table) {
      form.reset({
        name: table.name,
        capacity: table.capacity,
        status: table.status,
        spaceId: table.spaceId || '',
      });
    } else {
      form.reset({ // Reset for new form
        name: '',
        capacity: 1,
        status: 'available',
        spaceId: '',
      });
    }
  }, [table, form]);

  const handleSubmit = (values: TableFormValues) => {
    onSubmit(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="e.g., Table 1, Bar Seat 5" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="capacity"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Capacity</FormLabel>
              <FormControl>
                <Input type="number" placeholder="e.g., 4" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Status</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="available">Available</SelectItem>
                  <SelectItem value="occupied">Occupied</SelectItem>
                  <SelectItem value="reserved">Reserved</SelectItem>
                  <SelectItem value="out-of-service">Out of Service</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="spaceId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Space</FormLabel>
              <Select onValueChange={field.onChange} value={field.value || undefined}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a space" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  {spaces.map((space) => (
                    <SelectItem key={space.id} value={space.id}>
                      {space.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">{table ? 'Save Changes' : 'Create Table'}</Button>
        </div>
      </form>
    </Form>
  );
};

export default TableForm;
