'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useTablesStore } from '@/store/store';
import { Table as TableType } from '@/types/table';
import TableList from './TableList';
import TableForm from './TableForm';
import { PlusCircle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

const TablesPage: React.FC = () => {
  const { tables, addTable, updateTable, deleteTable } = useTablesStore();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedTable, setSelectedTable] = useState<TableType | undefined>(
    undefined
  );

  const handleAddTable = () => {
    setSelectedTable(undefined);
    setIsDialogOpen(true);
  };

  const handleEditTable = (table: TableType) => {
    setSelectedTable(table);
    setIsDialogOpen(true);
  };

  const handleDeleteTable = (id: string) => {
    deleteTable(id);
  };

  const handleFormSubmit = (data: Partial<TableType>) => {
    if (selectedTable) {
      updateTable(selectedTable.id, data);
    } else {
      // For adding, ensure all required fields are present or provide defaults
      const newTableData: TableType = {
        id: `table-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`, // More robust ID
        name: data.name!,
        capacity: data.capacity!,
        status: data.status!,
        spaceId: data.spaceId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...data, // Spread the rest of the data
      };
      addTable(newTableData);
    }
    setIsDialogOpen(false);
    setSelectedTable(undefined);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setSelectedTable(undefined);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Manage Tables</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleAddTable}>
              <PlusCircle className="mr-2 h-4 w-4" /> Add Table
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>
                {selectedTable ? 'Edit Table' : 'Add New Table'}
              </DialogTitle>
            </DialogHeader>
            <TableForm
              table={selectedTable}
              onSubmit={handleFormSubmit}
              onCancel={handleDialogClose}
            />
          </DialogContent>
        </Dialog>
      </div>

      <TableList
        tables={tables}
        onEdit={handleEditTable}
        onDelete={handleDeleteTable}
      />
    </div>
  );
};

export default TablesPage;
