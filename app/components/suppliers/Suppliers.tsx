
import { useState } from "react";
import { useSuppliersStore } from "@/store/store";
import { Supplier } from "@/types/supplier";
import { SupplierList } from "./SupplierList";
import { SupplierForm } from "./SupplierForm";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

export function Suppliers() {
  const suppliers = useSuppliersStore((state) => state.suppliers);
  const addSupplier = useSuppliersStore((state) => state.addSupplier);
  const updateSupplier = useSuppliersStore((state) => state.updateSupplier);
  const deleteSupplier = useSuppliersStore((state) => state.deleteSupplier);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | undefined>(undefined);

  const handleEdit = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setIsDialogOpen(true);
  };

  const handleDelete = (supplierId: string) => {
    deleteSupplier(supplierId);
  };

  const handleFormSubmit = (data: Omit<Supplier, 'id'>) => {
    if (selectedSupplier) {
      updateSupplier({ ...selectedSupplier, ...data });
    } else {
      addSupplier({ ...data, id: Date.now().toString(), productsSupplied: [] });
    }
    setSelectedSupplier(undefined);
    setIsDialogOpen(false);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Suppliers</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setSelectedSupplier(undefined)}>Add Supplier</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{selectedSupplier ? "Edit" : "Add"} Supplier</DialogTitle>
            </DialogHeader>
            <SupplierForm onSubmit={handleFormSubmit} supplier={selectedSupplier} />
          </DialogContent>
        </Dialog>
      </div>
      <SupplierList suppliers={suppliers} onEdit={handleEdit} onDelete={handleDelete} />
    </div>
  );
}
