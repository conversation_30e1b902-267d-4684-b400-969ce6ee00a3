
import { Supplier } from "@/types/supplier";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";

interface SupplierListProps {
  suppliers: Supplier[];
  onEdit: (supplier: Supplier) => void;
  onDelete: (supplierId: string) => void;
}

export function SupplierList({ suppliers, onEdit, onDelete }: SupplierListProps) {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Contact Person</TableHead>
          <TableHead>Phone</TableHead>
          <TableHead>Email</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {suppliers.map((supplier) => (
          <TableRow key={supplier.id}>
            <TableCell>{supplier.name}</TableCell>
            <TableCell>{supplier.contactPerson}</TableCell>
            <TableCell>{supplier.phone}</TableCell>
            <TableCell>{supplier.email}</TableCell>
            <TableCell>
              <Button variant="outline" size="sm" onClick={() => onEdit(supplier)}>
                Edit
              </Button>
              <Button variant="destructive" size="sm" onClick={() => onDelete(supplier.id)} className="ml-2">
                Delete
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
