import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  type ColumnDef,
} from '@tanstack/react-table';
import { Link } from 'react-router';
import { FaFilter, FaCalendarAlt } from 'react-icons/fa';
// import DatePicker from 'react-datepicker';
// import 'react-datepicker/dist/react-datepicker.css';

const OrdersTable = () => {
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const [filters, setFilters] = useState({
    status: '',
    startDate: null,
    endDate: null,
  });

  //   const { data, isLoading, isError } = useQuery({
  //     queryKey: ['orders', pagination, filters],
  //     queryFn: async () => {
  //       const params = new URLSearchParams({
  //         page: pagination.pageIndex + 1,
  //         pageSize: pagination.pageSize,
  //         status: filters.status,
  //         startDate: filters.startDate?.toISOString(),
  //         endDate: filters.endDate?.toISOString(),
  //       });

  //       const response = await fetch(`/api/orders?${params}`);
  //       return response.json();
  //     },
  //     keepPreviousData: true,
  //   });

  const { data, isLoading, isError } = useQuery({
    queryKey: ['restaurants', pagination, filters],
    queryFn: async () => {
      const response = await fetch('http://localhost:3300/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            query Restaurants {
                restaurants {
                  id
                  name
                }
            }
          `,
          variables: {},
        }),
      });
      return response.json();
    },
  });

  const columns: ColumnDef[] = [
    {
      accessorKey: 'id',
      header: 'Order ID',
      cell: ({ row }) => (
        <Link
          to={`/orders/${row.original.id}`}
          className='text-blue-600 hover:underline'
        >
          #{row.getValue('id')}
        </Link>
      ),
    },
    {
      accessorKey: 'customer',
      header: 'Customer',
      cell: ({ row }) => (
        <div className='flex items-center gap-2'>
          <span>{row.original.customer.name}</span>
          <span className='text-gray-500'>({row.original.customer.email})</span>
        </div>
      ),
    },
    {
      accessorKey: 'amount',
      header: 'Amount',
      cell: ({ row }) => (
        <span className='font-medium'>
          ${parseFloat(row.getValue('amount')).toFixed(2)}
        </span>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.getValue('status');
        const statusColors = {
          PENDING: 'bg-yellow-100 text-yellow-800',
          COMPLETED: 'bg-green-100 text-green-800',
          CANCELLED: 'bg-red-100 text-red-800',
        };

        return (
          <span
            className={`px-2 py-1 rounded-full text-sm ${statusColors[status]}`}
          >
            {status}
          </span>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Date',
      cell: ({ row }) =>
        new Date(row.getValue('createdAt')).toLocaleDateString(),
    },
  ];

  const table = useReactTable({
    data: data?.orders || [],
    columns,
    pageCount: data?.pageCount || -1,
    state: { pagination },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true,
  });

  if (isLoading)
    return <div className='text-center py-8'>Loading orders...</div>;
  if (isError)
    return (
      <div className='text-center py-8 text-red-600'>Error loading orders</div>
    );

  return (
    <div className='p-6 bg-white rounded-lg shadow-sm'>
      {/* Filters */}
      <div className='flex flex-col sm:flex-row gap-4 mb-6'>
        <div className='flex items-center gap-2'>
          <FaFilter className='text-gray-500' />
          <select
            value={filters.status}
            onChange={(e) => setFilters({ ...filters, status: e.target.value })}
            className='px-3 py-2 border rounded-md'
          >
            <option value=''>All Statuses</option>
            <option value='PENDING'>Pending</option>
            <option value='COMPLETED'>Completed</option>
            <option value='CANCELLED'>Cancelled</option>
          </select>
        </div>

        {/* <div className='flex items-center gap-2'>
          <FaCalendarAlt className='text-gray-500' />
          <DatePicker
            selected={filters.startDate}
            onChange={(date) => setFilters({ ...filters, startDate: date })}
            selectsStart
            startDate={filters.startDate}
            endDate={filters.endDate}
            placeholderText='Start Date'
            className='px-3 py-2 border rounded-md'
          />
          <DatePicker
            selected={filters.endDate}
            onChange={(date) => setFilters({ ...filters, endDate: date })}
            selectsEnd
            startDate={filters.startDate}
            endDate={filters.endDate}
            minDate={filters.startDate}
            placeholderText='End Date'
            className='px-3 py-2 border rounded-md'
          />
        </div> */}
      </div>

      {/* Table */}
      <div className='overflow-x-auto'>
        <table className='w-full'>
          <thead className='bg-gray-50'>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className='px-4 py-3 text-left text-sm font-medium text-gray-700'
                  >
                    {header.column.columnDef.header}
                  </th>
                ))}
              </tr>
            ))}
          </thead>

          <tbody className='divide-y divide-gray-200'>
            {table.getRowModel().rows.map((row) => (
              <tr key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <td key={cell.id} className='px-4 py-3 text-sm text-gray-700'>
                    {cell.renderCell()}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className='flex items-center justify-between mt-6'>
        <div className='flex gap-2'>
          <button
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className='px-3 py-1 border rounded-md disabled:opacity-50'
          >
            Previous
          </button>
          <button
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className='px-3 py-1 border rounded-md disabled:opacity-50'
          >
            Next
          </button>
        </div>

        <div className='flex items-center gap-4'>
          <span>
            Page {table.getState().pagination.pageIndex + 1} of{' '}
            {table.getPageCount()}
          </span>
          <select
            value={pagination.pageSize}
            onChange={(e) => table.setPageSize(Number(e.target.value))}
            className='px-2 py-1 border rounded-md'
          >
            {[10, 20, 30, 40, 50].map((pageSize) => (
              <option key={pageSize} value={pageSize}>
                Show {pageSize}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );
};

export default OrdersTable;
