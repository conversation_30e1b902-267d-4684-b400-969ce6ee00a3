import { useState } from 'react';
import { IngredientCategory } from '@/types/ingredient';
import { useIngredientCategoriesStore } from '@/store/store';
import IngredientCategoryList from '@/components/ingredients/IngredientCategoryList';
import IngredientCategoryForm from '@/components/ingredients/IngredientCategoryForm';

const IngredientCategoriesPage = () => {
  const { ingredientCategories, addIngredientCategory, deleteIngredientCategory, updateIngredientCategory } =
    useIngredientCategoriesStore();

  const [isCategoryFormOpen, setIsCategoryFormOpen] = useState(false);
  const [currentCategory, setCurrentCategory] = useState<IngredientCategory | undefined>(
    undefined
  );

  // Category handlers
  const handleAddCategory = () => {
    setCurrentCategory(undefined);
    setIsCategoryFormOpen(true);
  };

  const handleEditCategory = (category: IngredientCategory) => {
    setCurrentCategory(category);
    setIsCategoryFormOpen(true);
  };

  const handleSaveCategory = (category: IngredientCategory) => {
    const index = ingredientCategories.findIndex((c) => c.id === category.id);
    if (index >= 0) {
      updateIngredientCategory(category.id, category);
    } else {
      addIngredientCategory(category);
    }
  };

  const handleDeleteCategory = (categoryId: string) => {
    deleteIngredientCategory(categoryId);
  };

  return (
    <div className='space-y-6'>
      <h1 className='text-3xl font-bold'>Ingredient Categories</h1>
      <p className='text-muted-foreground'>
        Manage categories for your ingredients.
      </p>

      <IngredientCategoryList
        categories={ingredientCategories}
        onAddCategory={handleAddCategory}
        onEditCategory={handleEditCategory}
        onDeleteCategory={handleDeleteCategory}
      />

      {isCategoryFormOpen && (
        <IngredientCategoryForm
          isOpen={isCategoryFormOpen}
          onClose={() => setIsCategoryFormOpen(false)}
          onSave={handleSaveCategory}
          category={currentCategory}
        />
      )}
    </div>
  );
};

export default IngredientCategoriesPage;
