import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Ingredient, IngredientCategory } from '@/types/ingredient';
import { useIngredientCategoriesStore } from '@/store/store';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';

interface IngredientFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (ingredient: Ingredient) => void;
  ingredient?: Ingredient;
}

// Create a schema for form validation
const formSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  description: z.string().min(5, 'Description must be at least 5 characters'),
  unit: z.string().min(1, 'Unit is required'),
  categoryId: z.string().min(1, 'Category is required'),
  inStock: z.boolean().default(true),
  allergen: z.boolean().default(false),
  allergenType: z.string().optional(),
  stockLevel: z.number().min(0, 'Stock level must be a positive number'),
  unitOfMeasure: z.string().min(1, 'Unit of measure is required'),
  lowStockThreshold: z.number().min(0, 'Low stock threshold must be a positive number'),
  supplierId: z.string().min(1, 'Supplier is required'),
});

type FormValues = z.infer<typeof formSchema>;

const IngredientForm = ({
  isOpen,
  onClose,
  onSave,
  ingredient,
}: IngredientFormProps) => {
  const { ingredientCategories } = useIngredientCategoriesStore();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: ingredient?.name || '',
      description: ingredient?.description || '',
      unit: ingredient?.unit || '',
      categoryId: ingredient?.categoryId || '',
      inStock: ingredient?.inStock || true,
      allergen: ingredient?.allergen || false,
      allergenType: ingredient?.allergenType || '',
      stockLevel: ingredient?.stockLevel || 0,
      unitOfMeasure: ingredient?.unitOfMeasure || '',
      lowStockThreshold: ingredient?.lowStockThreshold || 0,
      supplierId: ingredient?.supplierId || '',
    },
  });

  const watchAllergen = form.watch('allergen');

  useEffect(() => {
    if (ingredient) {
      form.reset({
        name: ingredient.name,
        description: ingredient.description,
        unit: ingredient.unit,
        categoryId: ingredient.categoryId,
        inStock: ingredient.inStock,
        allergen: ingredient.allergen,
        allergenType: ingredient.allergenType || '',
        stockLevel: ingredient.stockLevel,
        unitOfMeasure: ingredient.unitOfMeasure,
        lowStockThreshold: ingredient.lowStockThreshold,
        supplierId: ingredient.supplierId,
      });
    } else {
      form.reset({
        name: '',
        description: '',
        unit: '',
        categoryId: '',
        inStock: true,
        allergen: false,
        allergenType: '',
        stockLevel: 0,
        unitOfMeasure: '',
        lowStockThreshold: 0,
        supplierId: '',
      });
    }
  }, [ingredient, form]);

  const handleSubmit = (values: FormValues) => {
    const ingredientData: Ingredient = {
      id: ingredient?.id || crypto.randomUUID(),
      name: values.name,
      description: values.description,
      unit: values.unit,
      categoryId: values.categoryId,
      inStock: values.inStock,
      allergen: values.allergen,
      allergenType: values.allergen ? values.allergenType : undefined,
      stockLevel: values.stockLevel,
      unitOfMeasure: values.unitOfMeasure,
      lowStockThreshold: values.lowStockThreshold,
      supplierId: values.supplierId,
    };

    onSave(ingredientData);
    onClose();
  };

  const units = ['kg', 'g', 'liter', 'ml', 'piece', 'dozen', 'tbsp', 'tsp'];

  const allergenTypes = [
    'Dairy',
    'Eggs',
    'Fish',
    'Shellfish',
    'Tree Nuts',
    'Peanuts',
    'Wheat',
    'Soy',
    'Sesame',
    'Gluten',
    'Seafood',
    'Other',
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-[500px]'>
        <DialogHeader>
          <DialogTitle>
            {ingredient ? 'Edit Ingredient' : 'Add New Ingredient'}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-4 py-4'
          >
            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder='Ingredient name' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Input placeholder='Brief description' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='categoryId'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select category' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {ingredientCategories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='unit'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Unit</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select unit' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {units.map((unit) => (
                          <SelectItem key={unit} value={unit}>
                            {unit}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='inStock'
                render={({ field }) => (
                  <FormItem className='flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4'>
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className='space-y-1 leading-none'>
                      <FormLabel>In Stock</FormLabel>
                      <FormDescription>
                        Is this ingredient currently available?
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='allergen'
                render={({ field }) => (
                  <FormItem className='flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4'>
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className='space-y-1 leading-none'>
                      <FormLabel>Allergen</FormLabel>
                      <FormDescription>
                        Is this ingredient an allergen?
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            {watchAllergen && (
              <FormField
                control={form.control}
                name='allergenType'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Allergen Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select allergen type' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {allergenTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter>
              <Button type='button' variant='outline' onClick={onClose}>
                Cancel
              </Button>
              <Button type='submit'>Save</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default IngredientForm;
