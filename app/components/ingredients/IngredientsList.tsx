import { useState } from 'react';
import { toast } from 'sonner';
import { DeleteConfirmation } from '@/components/ui/delete-confirmation';
import { Ingredient } from '@/types/ingredient';
import { useIngredientCategoriesStore } from '@/store/store';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  MoreVertical,
  Pencil,
  Plus,
  Search,
  Trash2,
  Filter,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface IngredientsListProps {
  ingredients: Ingredient[];
  onAddIngredient: () => void;
  onEditIngredient: (ingredient: Ingredient) => void;
  onDeleteIngredient: (id: string) => void;
}

const IngredientsList = ({
  ingredients,
  onAddIngredient,
  onEditIngredient,
  onDeleteIngredient,
}: IngredientsListProps) => {
  const { ingredientCategories } = useIngredientCategoriesStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [allergenFilter, setAllergenFilter] = useState<string>('all');
  const [stockFilter, setStockFilter] = useState<string>('all');
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [ingredientToDelete, setIngredientToDelete] = useState<string | null>(
    null
  );

  // Get category name by ID
  const getCategoryNameById = (categoryId: string): string => {
    const category = ingredientCategories.find((c) => c.id === categoryId);
    return category ? category.name : 'Unknown';
  };

  // Filter ingredients based on search query and filters
  const filteredIngredients = ingredients.filter((ingredient) => {
    const matchesSearch =
      ingredient.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ingredient.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory =
      categoryFilter === 'all' || ingredient.categoryId === categoryFilter;

    const matchesAllergen =
      allergenFilter === 'all' ||
      (allergenFilter === 'true' && ingredient.allergen) ||
      (allergenFilter === 'false' && !ingredient.allergen);

    const matchesStock =
      stockFilter === 'all' ||
      (stockFilter === 'true' && ingredient.inStock) ||
      (stockFilter === 'false' && !ingredient.inStock);

    return matchesSearch && matchesCategory && matchesAllergen && matchesStock;
  });

  // Use the predefined categories for filter dropdown

  const handleDeleteClick = (id: string) => {
    setIngredientToDelete(id);
    setDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = () => {
    if (ingredientToDelete) {
      onDeleteIngredient(ingredientToDelete);
      toast.success('Ingredient deleted successfully');
      setDeleteConfirmOpen(false);
      setIngredientToDelete(null);
    }
  };

  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
        <CardTitle>Ingredients</CardTitle>
        <div className='flex items-center gap-2'>
          <div className='relative'>
            <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
            <Input
              type='search'
              placeholder='Search ingredients...'
              className='w-[250px] pl-8'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button onClick={onAddIngredient}>
            <Plus className='h-4 w-4 mr-2' />
            Add Ingredient
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className='flex gap-4 mb-4'>
          <div className='flex items-center gap-2'>
            <Filter className='h-4 w-4 text-muted-foreground' />
            <span className='text-sm font-medium'>Filters:</span>
          </div>

          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className='w-[180px]'>
              <SelectValue placeholder='Category' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Categories</SelectItem>
              {ingredientCategories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={allergenFilter} onValueChange={setAllergenFilter}>
            <SelectTrigger className='w-[180px]'>
              <SelectValue placeholder='Allergen Status' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All</SelectItem>
              <SelectItem value='true'>Allergens</SelectItem>
              <SelectItem value='false'>Non-Allergens</SelectItem>
            </SelectContent>
          </Select>

          <Select value={stockFilter} onValueChange={setStockFilter}>
            <SelectTrigger className='w-[180px]'>
              <SelectValue placeholder='Stock Status' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All</SelectItem>
              <SelectItem value='true'>In Stock</SelectItem>
              <SelectItem value='false'>Out of Stock</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className='rounded-md border'>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Unit</TableHead>
                <TableHead>Allergen</TableHead>
                <TableHead>Stock Status</TableHead>
                <TableHead className='text-right'>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredIngredients.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className='h-24 text-center'>
                    No ingredients found.
                  </TableCell>
                </TableRow>
              ) : (
                filteredIngredients.map((ingredient) => (
                  <TableRow key={ingredient.id}>
                    <TableCell className='font-medium'>
                      <div>
                        <div>{ingredient.name}</div>
                        <div className='text-sm text-muted-foreground'>
                          {ingredient.description}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getCategoryNameById(ingredient.categoryId)}
                    </TableCell>
                    <TableCell>{ingredient.unit}</TableCell>
                    <TableCell>
                      {ingredient.allergen ? (
                        <Badge variant='destructive'>
                          {ingredient.allergenType || 'Allergen'}
                        </Badge>
                      ) : (
                        <Badge variant='outline'>None</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {ingredient.inStock ? (
                        <Badge variant='success'>In Stock</Badge>
                      ) : (
                        <Badge variant='secondary'>Out of Stock</Badge>
                      )}
                    </TableCell>
                    <TableCell className='text-right'>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant='ghost' className='h-8 w-8 p-0'>
                            <span className='sr-only'>Open menu</span>
                            <MoreVertical className='h-4 w-4' />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align='end'>
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => onEditIngredient(ingredient)}
                          >
                            <Pencil className='mr-2 h-4 w-4' />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className='text-destructive focus:text-destructive'
                            onClick={() => handleDeleteClick(ingredient.id)}
                          >
                            <Trash2 className='mr-2 h-4 w-4' />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      <DeleteConfirmation
        isOpen={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
        onConfirm={handleConfirmDelete}
        title='Delete Ingredient'
        description='Are you sure you want to delete this ingredient? This action cannot be undone.'
      />
    </Card>
  );
};

export default IngredientsList;
