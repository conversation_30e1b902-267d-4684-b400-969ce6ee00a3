import { useState } from 'react';
import { Ingredient } from '@/types/ingredient';
import { useIngredientsStore } from '@/store/store';
import IngredientsList from '@/components/ingredients/IngredientsList';
import IngredientForm from '@/components/ingredients/IngredientForm';

const IngredientsPage = () => {
  const { ingredients, addIngredient, deleteIngredient, updateIngredient } =
    useIngredientsStore();

  const [isIngredientFormOpen, setIsIngredientFormOpen] = useState(false);
  const [currentIngredient, setCurrentIngredient] = useState<Ingredient | undefined>(
    undefined
  );

  // Ingredient handlers
  const handleAddIngredient = () => {
    setCurrentIngredient(undefined);
    setIsIngredientFormOpen(true);
  };

  const handleEditIngredient = (ingredient: Ingredient) => {
    setCurrentIngredient(ingredient);
    setIsIngredientFormOpen(true);
  };

  const handleSaveIngredient = (ingredient: Ingredient) => {
    const index = ingredients.findIndex((i) => i.id === ingredient.id);
    if (index >= 0) {
      updateIngredient(ingredient.id, ingredient);
    } else {
      addIngredient(ingredient);
    }
  };

  const handleDeleteIngredient = (ingredientId: string) => {
    deleteIngredient(ingredientId);
  };

  return (
    <div className='space-y-6'>
      <h1 className='text-3xl font-bold'>Ingredients Management</h1>
      <p className='text-muted-foreground'>
        Create and manage ingredients for your menu items.
      </p>

      <IngredientsList
        ingredients={ingredients}
        onAddIngredient={handleAddIngredient}
        onEditIngredient={handleEditIngredient}
        onDeleteIngredient={handleDeleteIngredient}
      />

      {isIngredientFormOpen && (
        <IngredientForm
          isOpen={isIngredientFormOpen}
          onClose={() => setIsIngredientFormOpen(false)}
          onSave={handleSaveIngredient}
          ingredient={currentIngredient}
        />
      )}
    </div>
  );
};

export default IngredientsPage;
