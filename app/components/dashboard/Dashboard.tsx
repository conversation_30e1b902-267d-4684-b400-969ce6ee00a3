import StatCard from '@/components/dashboard/StatCard';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Users, Briefcase, UtensilsCrossed, DollarSign } from 'lucide-react';
import { useEmployeesStore, useRolesStore } from '@/store/store';
import RolesPage from '../roles/Roles';
import EmployeesPage from '../employees/Employees';

const Dashboard = () => {
  const { employees } = useEmployeesStore();
  const { roles } = useRolesStore();

  return (
    <div className='space-y-6'>
      <h1 className='text-3xl font-bold'>Dashboard</h1>

      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <StatCard
          title='Total Employees'
          value={employees
            .filter((e) => e.status === 'Active')
            .length.toString()}
          description='Active employees'
          icon={<Users className='h-4 w-4' />}
        />
        <StatCard
          title='Roles'
          value={roles.length.toString()}
          description='Available positions'
          icon={<Briefcase className='h-4 w-4' />}
        />
        <StatCard
          title='Menu Items'
          value='48'
          description='Active items'
          icon={<UtensilsCrossed className='h-4 w-4' />}
        />
        <StatCard
          title='Revenue'
          value='$12,465'
          description='This month'
          icon={<DollarSign className='h-4 w-4' />}
        />
      </div>

      <Tabs defaultValue='employees' className='space-y-4'>
        <TabsHeader />

        <TabsContent value='employees' className='space-y-4'>
          <EmployeesPage />
        </TabsContent>

        <TabsContent value='roles' className='space-y-4'>
          <RolesPage />
        </TabsContent>
      </Tabs>
    </div>
  );
};

const TabsHeader = () => (
  <div className='flex justify-between'>
    <TabsList>
      <TabsTrigger value='employees'>Employees</TabsTrigger>
      <TabsTrigger value='roles'>Roles</TabsTrigger>
    </TabsList>
  </div>
);

export default Dashboard;
