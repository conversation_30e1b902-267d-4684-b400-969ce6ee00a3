import React, { useState } from 'react';
import { Link, useLocation } from 'react-router';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  Users,
  Briefcase,
  UtensilsCrossed,
  Home,
  Settings,
  Building,
  ShoppingCart,
  ChevronLeft,
  ChevronRight,
  Apple,
  Tags,
  SquareStack, // Import SquareStack icon
  Landmark, // Import Landmark icon
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

type NavItem = {
  name: string;
  path: string;
  icon: React.ReactNode;
};

const navItems: NavItem[] = [
  {
    name: 'Dashboard',
    path: '/dashboard',
    icon: <LayoutDashboard className='h-5 w-5' />,
  },
  {
    name: 'Employees',
    path: '/dashboard/employees',
    icon: <Users className='h-5 w-5' />,
  },
  {
    name: 'Roles',
    path: '/dashboard/roles',
    icon: <Briefcase className='h-5 w-5' />,
  },
  {
    name: 'Departments',
    path: '/dashboard/departments',
    icon: <Building className='h-5 w-5' />,
  },
  {
    name: 'Spaces',
    path: '/dashboard/spaces',
    icon: <Landmark className='h-5 w-5' />, // Add Spaces nav item
  },
  {
    name: 'Menu',
    path: '/dashboard/menu',
    icon: <UtensilsCrossed className='h-5 w-5' />,
  },
  {
    name: 'Ingredients',
    path: '/dashboard/ingredients',
    icon: <Apple className='h-5 w-5' />,
  },
  {
    name: 'Ingredient Categories',
    path: '/dashboard/ingredient-categories',
    icon: <Tags className='h-5 w-5' />,
  },
  {
    name: 'Orders',
    path: '/dashboard/orders',
    icon: <ShoppingCart className='h-5 w-5' />,
  },
  {
    name: 'Tables',
    path: '/dashboard/tables',
    icon: <SquareStack className='h-5 w-5' />, // Add Tables nav item
  },
];

const Sidebar = () => {
  const location = useLocation();
  const [collapsed, setCollapsed] = useState(() => {
    const savedState = window?.localStorage?.getItem('sidebarCollapsed');
    return savedState ? JSON.parse(savedState) : false;
  });

  const toggleSidebar = () => {
    const newState = !collapsed;
    setCollapsed(newState);
    window?.localStorage?.setItem('sidebarCollapsed', JSON.stringify(newState));
  };

  return (
    <div
      className={cn(
        'flex h-full flex-col border-r bg-sidebar transition-all duration-300 ease-in-out',
        collapsed ? 'w-[50px]' : 'w-[250px]'
      )}
    >
      <div
        className={cn(
          'flex items-center border-b',
          collapsed ? 'justify-center py-3 px-1' : 'justify-between py-4 px-4'
        )}
      >
        {!collapsed && (
          <h2 className='font-semibold text-sidebar-foreground'>Admin Panel</h2>
        )}
        <button
          onClick={toggleSidebar}
          className='rounded-md p-1 text-sidebar-foreground hover:bg-sidebar-accent/50 cursor-pointer'
          aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          title={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {collapsed ? (
            <ChevronRight className='h-5 w-5' />
          ) : (
            <ChevronLeft className='h-5 w-5' />
          )}
        </button>
      </div>
      <nav className={cn('flex-1', collapsed ? 'px-1 py-2' : 'p-2')}>
        <ul
          className={cn('space-y-2', collapsed && 'flex flex-col items-center')}
        >
          {navItems.map((item) => (
            <li key={item.path}>
              {collapsed ? (
                <TooltipProvider delayDuration={0}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link
                        to={item.path}
                        className={cn(
                          'flex h-8 w-8 items-center justify-center rounded-md transition-colors',
                          location.pathname === item.path
                            ? 'bg-sidebar-accent text-sidebar-accent-foreground'
                            : 'text-sidebar-foreground hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground'
                        )}
                      >
                        {item.icon}
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent side='right'>{item.name}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : (
                <Link
                  to={item.path}
                  className={cn(
                    'flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors',
                    location.pathname === item.path
                      ? 'bg-sidebar-accent text-sidebar-accent-foreground'
                      : 'text-sidebar-foreground hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground'
                  )}
                >
                  {item.icon}
                  {item.name}
                </Link>
              )}
            </li>
          ))}
        </ul>
      </nav>
      {!collapsed && (
        <div className='border-t p-4'>
          <p className='text-xs text-sidebar-foreground/60'>
            Bistro Management v1.0
          </p>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
