import Sidebar from './Sidebar';
import { Outlet, useLocation } from 'react-router';
import ErrorBoundary from '../errors/ErrorBoundary';

const Layout = () => {
  const location = useLocation();
  return (
    <div className='flex h-screen flex-col'>
      {/* <Header /> */}
      <div className='flex flex-1 overflow-hidden'>
        <Sidebar />
        <main className='flex-1 overflow-y-auto p-6'>
          <ErrorBoundary>
            <div className='route-transition' key={location.pathname}>
              <Outlet />
            </div>
          </ErrorBoundary>
        </main>
      </div>
    </div>
  );
};

export default Layout;
