import { useState } from 'react';
import { toast } from 'sonner';
import { DeleteConfirmation } from '@/components/ui/delete-confirmation';
import { Role } from '@/types/role';
import { Employee } from '@/types/employee';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '../ui/input';
import { MoreVertical, Pencil, Plus, Search, Trash2 } from 'lucide-react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';

interface RoleListProps {
  roles: Role[];
  employees: Employee[];
  onAddRole: () => void;
  onEditRole: (role: Role) => void;
  onDeleteRole: (roleId: string) => void;
}

const RoleList = ({
  roles,
  employees,
  onAddRole,
  onEditRole,
  onDeleteRole,
}: RoleListProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null);

  const filteredRoles = roles.filter(
    (role) =>
      role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      role.department.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getEmployeeCountForRole = (roleId: string) => {
    return employees.filter((employee) => employee.role.id === roleId).length;
  };

  const handleDeleteClick = (role: Role) => {
    const employeeCount = getEmployeeCountForRole(role.id);

    if (employeeCount > 0) {
      toast.error(
        `Cannot delete role: This role is assigned to ${employeeCount} employee${
          employeeCount > 1 ? 's' : ''
        }. Please remove all assignments first.`
      );
      return;
    }

    setRoleToDelete(role);
  };

  const confirmDelete = () => {
    if (roleToDelete) {
      onDeleteRole(roleToDelete.id);
      toast.success(`${roleToDelete.name} role has been removed successfully.`);
      setRoleToDelete(null);
    }
  };

  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
        <CardTitle>Roles</CardTitle>
        <div className='flex items-center gap-2'>
          <div className='relative'>
            <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
            <Input
              type='search'
              placeholder='Search roles...'
              className='w-[250px] pl-8'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button onClick={onAddRole}>
            <Plus className='h-4 w-4' />
            New
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Department</TableHead>
              <TableHead>Employees</TableHead>
              <TableHead className='text-right'>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredRoles.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={6}
                  className='text-center h-32 text-muted-foreground'
                >
                  No employees found
                </TableCell>
              </TableRow>
            ) : (
              filteredRoles.map((role) => (
                <TableRow key={role.id}>
                  <TableCell className='font-medium'>{role.name}</TableCell>
                  <TableCell>{role.department}</TableCell>
                  <TableCell>
                    <Badge
                      key={role.id}
                      variant='outline'
                      className='bg-secondary'
                    >
                      {getEmployeeCountForRole(role.id)} employees
                    </Badge>
                  </TableCell>
                  <TableCell className='text-right'>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant='ghost' size='icon'>
                          <MoreVertical className='h-4 w-4' />
                          <span className='sr-only'>Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align='end' className='bg-popover'>
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => onEditRole(role)}>
                          <Pencil className='mr-2 h-4 w-4' />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className='text-destructive focus:text-destructive'
                          onClick={() => handleDeleteClick(role)}
                        >
                          <Trash2 className='mr-2 h-4 w-4' />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmation
        isOpen={!!roleToDelete}
        onClose={() => setRoleToDelete(null)}
        onConfirm={confirmDelete}
        title='Delete Role'
        description={`Are you sure you want to delete the "${roleToDelete?.name}" role? This action cannot be undone.`}
      />
    </Card>
  );
};

export default RoleList;
