import React, { useEffect, useState } from 'react';
import { Role } from '@/types/role';
import { Department } from '@/types/department';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';

interface RoleFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (role: Role) => void;
  role?: Role;
  departments: Department[];
}

const RoleForm = ({
  isOpen,
  onClose,
  onSave,
  role,
  departments,
}: RoleFormProps) => {
  const isEditing = !!role;

  const form = useForm({
    defaultValues: {
      title: role?.name || '',
      departmentId: role?.departmentId || '',
      description: role?.description || '',
      permissions: role?.permissions || [],
    },
  });

  useEffect(() => {
    if (role) {
      form.reset({
        title: role.name,
        departmentId: role.departmentId || '',
        description: role.description,
        permissions: role.permissions,
      });
    } else {
      form.reset({
        title: '',
        departmentId: '',
        description: '',
        permissions: [],
      });
    }
  }, [role, form]);

  const availablePermissions = [
    'View Employees',
    'Manage Employees',
    'View Schedule',
    'Manage Schedule',
    'View Reports',
    'Manage Reports',
    'View Menu',
    'Manage Menu',
    'View Inventory',
    'Manage Inventory',
    'Admin Access',
  ];

  const handlePermissionChange = (permission: string, checked: boolean) => {
    const currentPermissions = form.getValues('permissions');

    if (checked) {
      form.setValue('permissions', [...currentPermissions, permission]);
    } else {
      form.setValue(
        'permissions',
        currentPermissions.filter((p) => p !== permission)
      );
    }
  };

  const handleSubmit = (values: {
    title: string;
    departmentId: string;
    description: string;
    permissions: string[];
  }) => {
    // Find the selected department
    const selectedDepartment = departments.find(
      (d) => d.id === values.departmentId
    );

    if (!selectedDepartment && values.departmentId) {
      alert('Selected department not found!');
      return;
    }

    const roleData: Role = {
      id: role?.id || crypto.randomUUID(),
      name: values.title,
      department: selectedDepartment?.name || '',
      departmentId: values.departmentId,
      description: values.description,
      permissions: values.permissions,
    };

    onSave(roleData);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className='sm:max-w-[500px]'>
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Edit Role' : 'Add New Role'}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update role information and permissions.'
              : 'Fill in the details to add a new role.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-6'
          >
            <FormField
              control={form.control}
              name='title'
              rules={{
                required: 'Title is required',
                minLength: {
                  value: 2,
                  message: 'Title must be at least 2 characters.',
                },
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder='Head Chef' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='departmentId'
              rules={{ required: 'Department is required' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Department</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select a department' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {departments.map((department) => (
                        <SelectItem key={department.id} value={department.id}>
                          {department.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Brief role description'
                      className='resize-none'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='space-y-4'>
              <FormLabel>Permissions</FormLabel>
              <div className='grid grid-cols-2 gap-4'>
                {availablePermissions.map((permission) => {
                  const permissions = form.getValues('permissions') || [];
                  const isChecked = permissions.includes(permission);

                  return (
                    <div
                      key={permission}
                      className='flex flex-row items-start space-x-3 space-y-0'
                    >
                      <Checkbox
                        id={`permission-${permission}`}
                        checked={isChecked}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(permission, checked as boolean)
                        }
                      />
                      <label
                        htmlFor={`permission-${permission}`}
                        className='text-sm font-normal cursor-pointer'
                      >
                        {permission}
                      </label>
                    </div>
                  );
                })}
              </div>
            </div>

            <DialogFooter>
              <Button type='button' variant='outline' onClick={onClose}>
                Cancel
              </Button>
              <Button type='submit'>{isEditing ? 'Update' : 'Create'}</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default RoleForm;
