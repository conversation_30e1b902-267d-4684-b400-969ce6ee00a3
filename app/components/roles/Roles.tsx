import RoleList from '@/components/roles/RoleList';
import RoleForm from '@/components/roles/RoleForm';
import { Role } from '@/types/role';
import {
  useDepartmentsStore,
  useEmployeesStore,
  useRolesStore,
} from '@/store/store';
import { useState } from 'react';

const RolesPage = () => {
  const { roles, addRole, deleteRole, updateRole } = useRolesStore();
  const { employees } = useEmployeesStore();
  const { departments } = useDepartmentsStore();
  const [isRoleFormOpen, setIsRoleFormOpen] = useState(false);
  const [currentRole, setCurrentRole] = useState<Role | undefined>(undefined);

  const handleAddRole = () => {
    setCurrentRole(undefined);
    setIsRoleFormOpen(true);
  };

  const handleEditRole = (role: Role) => {
    setCurrentRole(role);
    setIsRoleFormOpen(true);
  };

  const handleSaveRole = (role: Role) => {
    const index = roles.findIndex((r) => r.id === role.id);
    if (index >= 0) {
      updateRole(role.id, role);

      // Update employees with the updated role
      // setEmployees((prevEmployees) =>
      //   prevEmployees.map((employee) => ({
      //     ...employee,
      //     roles: employee.roles.map((r) => (r.id === role.id ? role : r)),
      //   }))
      // );
    } else {
      addRole(role);
    }
  };

  const handleDeleteRole = (roleId: string) => {
    deleteRole(roleId);
  };

  return (
    <div className='space-y-6'>
      <h1 className='text-3xl font-bold'>Roles Management</h1>
      <p className='text-muted-foreground'>
        Define and manage roles and job titles for your restaurant staff.
      </p>

      <RoleList
        roles={roles}
        employees={employees}
        onAddRole={handleAddRole}
        onEditRole={handleEditRole}
        onDeleteRole={handleDeleteRole}
      />

      {isRoleFormOpen && (
        <RoleForm
          isOpen={isRoleFormOpen}
          onClose={() => setIsRoleFormOpen(false)}
          onSave={handleSaveRole}
          role={currentRole}
          departments={departments}
        />
      )}
    </div>
  );
};

export default RolesPage;
