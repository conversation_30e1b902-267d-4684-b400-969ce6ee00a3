'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea'; // Import Textarea
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Space } from '@/types/space';

// Zod schema for space validation
const spaceSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
});

type SpaceFormValues = z.infer<typeof spaceSchema>;

interface SpaceFormProps {
  space?: Space;
  onSubmit: (data: Partial<SpaceFormValues>) => void;
  onCancel: () => void;
}

const SpaceForm: React.FC<SpaceFormProps> = ({
  space,
  onSubmit,
  onCancel,
}) => {
  const form = useForm<SpaceFormValues>({
    resolver: zodResolver(spaceSchema),
    defaultValues: {
      name: space?.name || '',
      description: space?.description || '',
    },
  });

  useEffect(() => {
    if (space) {
      form.reset({
        name: space.name,
        description: space.description || '',
      });
    } else {
      form.reset({
        name: '',
        description: '',
      });
    }
  }, [space, form]);

  const handleSubmit = (values: SpaceFormValues) => {
    onSubmit(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="e.g., Main Dining Area, Patio" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="A brief description of the space"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">{space ? 'Save Changes' : 'Create Space'}</Button>
        </div>
      </form>
    </Form>
  );
};

export default SpaceForm;
