'use client';

import React, { useState } from 'react';
import { Space } from '@/types/space';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Edit, Trash2 } from 'lucide-react';
import {DeleteConfirmation} from '@/components/ui/delete-confirmation';

interface SpaceListProps {
  spaces: Space[];
  onEdit: (space: Space) => void;
  onDelete: (id: string) => void;
}

const SpaceList: React.FC<SpaceListProps> = ({
  spaces,
  onEdit,
  onDelete,
}) => {
  const [deleteTarget, setDeleteTarget] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleDeleteClick = (id: string) => {
    setDeleteTarget(id);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (deleteTarget) {
      onDelete(deleteTarget);
    }
    setIsDeleteDialogOpen(false);
    setDeleteTarget(null);
  };

  const cancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setDeleteTarget(null);
  };

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {spaces.length > 0 ? (
              spaces.map((space) => (
                <TableRow key={space.id}>
                  <TableCell>{space.name}</TableCell>
                  <TableCell>{space.description || 'N/A'}</TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="outline"
                      size="icon"
                      className="mr-2"
                      onClick={() => onEdit(space)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="destructive"
                      size="icon"
                      onClick={() => handleDeleteClick(space.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={3} className="text-center">
                  No spaces found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {isDeleteDialogOpen && (
        <DeleteConfirmation
          isOpen={isDeleteDialogOpen}
          onConfirm={confirmDelete}
          onClose={cancelDelete}
          description={`Are you sure you want to delete space "${spaces.find((s) => s.id === deleteTarget)?.name || 'space'}"? This action cannot be undone.`}
        />
      )}
    </>
  );
};

export default SpaceList;
