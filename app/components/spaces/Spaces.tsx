'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { useSpacesStore } from '@/store/store';
import { Space } from '@/types/space';
import SpaceList from './SpaceList';
import SpaceForm from './SpaceForm';
import { PlusCircle } from 'lucide-react';
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

const SpacesPage: React.FC = () => {
  const { spaces, addSpace, updateSpace, deleteSpace } = useSpacesStore();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedSpace, setSelectedSpace] = useState<Space | undefined>(
    undefined
  );

  const handleAddSpace = () => {
    setSelectedSpace(undefined);
    setIsDialogOpen(true);
  };

  const handleEditSpace = (space: Space) => {
    setSelectedSpace(space);
    setIsDialogOpen(true);
  };

  const handleDeleteSpace = (id: string) => {
    deleteSpace(id);
  };

  const handleFormSubmit = (data: Partial<Space>) => {
    if (selectedSpace) {
      updateSpace(selectedSpace.id, data);
    } else {
      const newSpaceData: Space = {
        id: `space-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
        name: data.name!,
        description: data.description,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...data,
      };
      addSpace(newSpaceData);
    }
    setIsDialogOpen(false);
    setSelectedSpace(undefined);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setSelectedSpace(undefined);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Manage Spaces</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleAddSpace}>
              <PlusCircle className="mr-2 h-4 w-4" /> Add Space
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>
                {selectedSpace ? 'Edit Space' : 'Add New Space'}
              </DialogTitle>
            </DialogHeader>
            <SpaceForm
              space={selectedSpace}
              onSubmit={handleFormSubmit}
              onCancel={handleDialogClose}
            />
          </DialogContent>
        </Dialog>
      </div>

      <SpaceList
        spaces={spaces}
        onEdit={handleEditSpace}
        onDelete={handleDeleteSpace}
      />
    </div>
  );
};

export default SpacesPage;
