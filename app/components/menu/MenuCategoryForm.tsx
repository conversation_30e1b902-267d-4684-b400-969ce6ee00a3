import React, { useEffect } from 'react';
import { MenuCategory } from '@/types/menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMenuCategoriesStore } from '@/store/store'; // Import store
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'; // Import Select components

interface MenuCategoryFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (menuCategory: MenuCategory) => void;
  menuCategory?: MenuCategory;
  // menuCategories prop removed, will be fetched from store
}

// Create a schema for form validation
const formSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  description: z.string().min(5, 'Description must be at least 5 characters'),
  order: z.coerce.number().min(1, 'Order must be a positive number'),
  parentId: z.string().optional(), // Add parentId to schema
});

type FormValues = z.infer<typeof formSchema>;

const MenuCategoryForm = ({
  isOpen,
  onClose,
  onSave,
  menuCategory,
}: MenuCategoryFormProps) => {
  const { menuCategories } = useMenuCategoriesStore(); // Fetch categories from store
  const isEditing = !!menuCategory;

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: menuCategory?.name || '',
      description: menuCategory?.description || '',
      order: menuCategory?.order || 1,
      parentId: menuCategory?.parentId || '', // Initialize parentId
    },
  });

  useEffect(() => {
    if (menuCategory) {
      form.reset({
        name: menuCategory.name,
        description: menuCategory.description,
        order: menuCategory.order,
        parentId: menuCategory.parentId || '',
      });
    } else {
      form.reset({
        name: '',
        description: '',
        order: 1,
        parentId: '',
      });
    }
  }, [menuCategory, form]);

  const handleSubmit = (values: FormValues) => {
    const menuCategoryData: MenuCategory = {
      id: menuCategory?.id || crypto.randomUUID(),
      name: values.name,
      description: values.description,
      order: values.order,
      parentId: values.parentId || undefined, // Ensure empty string becomes undefined
    };

    onSave(menuCategoryData);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className='sm:max-w-[500px]'>
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Edit Category' : 'Add New Category'}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update category information.'
              : 'Fill in the details to add a new menu category.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-6'
          >
            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder='Appetizers' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Brief description of the category'
                      className='resize-none'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='order'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Display Order</FormLabel>
                  <FormControl>
                    <Input type='number' min='1' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="parentId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Parent Category (Optional)</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a parent category or leave empty" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">None (Top-level Category)</SelectItem>
                      {menuCategories
                        .filter(cat => cat.id !== menuCategory?.id) // Prevent self-parenting
                        .map((cat) => (
                          <SelectItem key={cat.id} value={cat.id}>
                            {cat.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type='button' variant='outline' onClick={onClose}>
                Cancel
              </Button>
              <Button type='submit'>{isEditing ? 'Update' : 'Create'}</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default MenuCategoryForm;
