import { useState } from 'react';
import { toast } from 'sonner';
import { DeleteConfirmation } from '@/components/ui/delete-confirmation';
import { MenuItem, MenuCategory } from '@/types/menu';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  MoreVertical,
  Pencil,
  Plus,
  Search,
  Trash2,
  // ArrowUp, // Not currently used, can be removed if not planned for reordering
  // ArrowDown, // Not currently used
  ChevronRight, // For indicating expandable rows, if we go that route
  ChevronDown,  // For indicating expanded rows
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface MenuCategoryListProps {
  menuCategories: MenuCategory[];
  menuItems: MenuItem[];
  onAddMenuCategory: () => void;
  onEditMenuCategory: (menuCategory: MenuCategory) => void;
  onDeleteMenuCategory: (menuCategoryId: string) => void;
}

const MenuCategoryList = ({
  menuCategories,
  menuItems,
  onAddMenuCategory,
  onEditMenuCategory,
  onDeleteMenuCategory,
}: MenuCategoryListProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryToDelete, setCategoryToDelete] = useState<MenuCategory | null>(null);
  // State to track expanded categories if we implement collapsible sections
  // const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});

  const getItemCountForCategory = (categoryId: string) => {
    return menuItems.filter((item) => item.categoryId === categoryId).length;
  };

  // Add explicit return type for recursive function
  const renderCategoriesRecursive = (parentId?: string, depth = 0): React.ReactElement[] => {
    const categoriesToRender = menuCategories
      .filter(
        (category) =>
          (category.parentId || undefined) === parentId && // Normalize parentId for comparison
          (category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
           category.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
           !searchQuery) // Show all if no search query, or match
      )
      .sort((a, b) => a.order - b.order);

    // If searching, and parent doesn't match, also check if any children match
    if (searchQuery && parentId) {
      const parentMatches = menuCategories.find(cat => cat.id === parentId && 
        (cat.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
         cat.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      if (!parentMatches && categoriesToRender.length === 0) {
         // If parent doesn't match and no direct children match,
         // we need to see if any deeper children match to render this branch
         const hasMatchingDescendant = menuCategories.some(cat => {
           let current = cat;
           while(current.parentId) {
             if(current.parentId === parentId) { // is a descendant
                if (cat.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    cat.description.toLowerCase().includes(searchQuery.toLowerCase())) {
                  return true;
                }
                break; 
             }
             const nextParent = menuCategories.find(p => p.id === current.parentId);
             if (!nextParent) break;
             current = nextParent;
           }
           return false;
         });
         if(!hasMatchingDescendant) return [];
      }
    }

    return categoriesToRender.flatMap((category) => {
      const children = renderCategoriesRecursive(category.id, depth + 1);
      return [
        (
          <TableRow key={category.id}>
            <TableCell style={{ paddingLeft: `${depth * 1.5}rem` }}>
              {category.order}
            </TableCell>
            <TableCell className='font-medium' style={{ paddingLeft: `${depth * 1.5}rem` }}>
              {/* Add expand/collapse icon if category has children - for future enhancement */}
              {category.name}
            </TableCell>
            <TableCell>{category.description}</TableCell>
            <TableCell>
              <Badge variant='outline' className='bg-secondary'>
                {getItemCountForCategory(category.id)} items
              </Badge>
            </TableCell>
            <TableCell className='text-right'>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant='ghost' size='icon'>
                    <MoreVertical className='h-4 w-4' />
                    <span className='sr-only'>Open menu for {category.name}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end' className='bg-popover'>
                  <DropdownMenuLabel>{category.name}</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => onEditMenuCategory(category)}>
                    <Pencil className='mr-2 h-4 w-4' />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className='text-destructive focus:text-destructive'
                    onClick={() => {
                      const itemsInCategory = getItemCountForCategory(category.id);
                      if (itemsInCategory > 0) {
                        toast.error(
                          `Cannot delete category: "${category.name}" contains ${itemsInCategory} menu item${
                            itemsInCategory > 1 ? 's' : ''
                          }. Please remove or reassign these items first.`
                        );
                        return;
                      }
                      const hasSubcategories = menuCategories.some(
                        (subCat) => subCat.parentId === category.id
                      );
                      if (hasSubcategories) {
                        toast.error(
                          `Cannot delete category: "${category.name}" has subcategories. Please remove or reassign them first.`
                        );
                        return;
                      }
                      setCategoryToDelete(category);
                    }}
                  >
                    <Trash2 className='mr-2 h-4 w-4' />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </TableCell>
          </TableRow>
        ),
        ...children,
      ];
    });
  };
  
  const displayedCategories = renderCategoriesRecursive();

  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
        <CardTitle>Menu Categories</CardTitle>
        <div className='flex items-center gap-2'>
          <div className='relative'>
            <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
            <Input
              type='search'
              placeholder='Search categories...'
              className='w-[250px] pl-8'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button onClick={onAddMenuCategory}>
            <Plus className='h-4 w-4 mr-2' />
            Add Category
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Order</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Items</TableHead>
              <TableHead className='text-right'>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {displayedCategories && displayedCategories.length > 0 ? (
              displayedCategories
            ) : (
              <TableRow>
                <TableCell
                  colSpan={5}
                  className='text-center h-32 text-muted-foreground'
                >
                  No categories found {searchQuery && 'matching your search'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmation
        isOpen={!!categoryToDelete}
        onClose={() => setCategoryToDelete(null)}
        onConfirm={() => {
          if (categoryToDelete) {
            onDeleteMenuCategory(categoryToDelete.id);
            toast.success(
              `${categoryToDelete.name} category has been removed.`
            );
            setCategoryToDelete(null);
          }
        }}
        title='Delete Menu Category'
        description={`Are you sure you want to delete the "${categoryToDelete?.name}" category? This action cannot be undone.`}
      />
    </Card>
  );
};

export default MenuCategoryList;
