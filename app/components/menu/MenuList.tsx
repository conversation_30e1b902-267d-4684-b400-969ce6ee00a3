import { useState } from 'react';
import { toast } from 'sonner';
import { DeleteConfirmation } from '@/components/ui/delete-confirmation';
import { MenuItem, MenuCategory, MenuItemStatus } from '@/types/menu';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  MoreVertical,
  Pencil,
  Plus,
  Search,
  Trash2,
  Filter,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface MenuListProps {
  menuItems: MenuItem[];
  menuCategories: MenuCategory[];
  onAddMenuItem: () => void;
  onEditMenuItem: (menuItem: MenuItem) => void;
  onDeleteMenuItem: (menuItemId: string) => void;
}

const MenuList = ({
  menuItems,
  menuCategories,
  onAddMenuItem,
  onEditMenuItem,
  onDeleteMenuItem,
}: MenuListProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState<MenuItemStatus | 'all'>(
    'all'
  );
  const [dietaryFilter, setDietaryFilter] = useState('all');
  const [menuItemToDelete, setMenuItemToDelete] = useState<MenuItem | null>(
    null
  );

  const filteredMenuItems = menuItems.filter((menuItem) => {
    // Search filter
    const matchesSearch =
      menuItem.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      menuItem.description.toLowerCase().includes(searchQuery.toLowerCase());

    // Category filter
    const matchesCategory =
      categoryFilter === 'all' || menuItem.categoryId === categoryFilter;

    // Status filter
    const matchesStatus =
      statusFilter === 'all' || menuItem.status === statusFilter;

    // Dietary filter
    let matchesDietary = true;
    if (dietaryFilter === 'vegetarian') {
      matchesDietary = menuItem.isVegetarian;
    } else if (dietaryFilter === 'vegan') {
      matchesDietary = menuItem.isVegan;
    } else if (dietaryFilter === 'gluten-free') {
      matchesDietary = menuItem.isGlutenFree;
    }

    return matchesSearch && matchesCategory && matchesStatus && matchesDietary;
  });

  // Sort by category order
  const sortedMenuItems = [...filteredMenuItems].sort((a, b) => {
    const categoryA = menuCategories.find((c) => c.id === a.categoryId);
    const categoryB = menuCategories.find((c) => c.id === b.categoryId);

    if (!categoryA || !categoryB) return 0;
    return categoryA.order - categoryB.order;
  });

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  const getCategoryName = (categoryId: string) => {
    const category = menuCategories.find((c) => c.id === categoryId);
    return category ? category.name : 'Uncategorized';
  };

  const getStatusBadgeVariant = (status: MenuItemStatus) => {
    switch (status) {
      case 'Available':
        return 'success';
      case 'Unavailable':
        return 'destructive';
      case 'Seasonal':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
        <CardTitle>Menu Items</CardTitle>
        <div className='flex items-center gap-2'>
          <div className='relative'>
            <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
            <Input
              type='search'
              placeholder='Search menu items...'
              className='w-[250px] pl-8'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button onClick={onAddMenuItem}>
            <Plus className='h-4 w-4 mr-2' />
            Add Item
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className='flex gap-4 mb-4'>
          <div className='flex items-center gap-2'>
            <Filter className='h-4 w-4 text-muted-foreground' />
            <span className='text-sm font-medium'>Filters:</span>
          </div>

          <div className='flex gap-2'>
            <Select
              value={categoryFilter}
              onValueChange={(value) => setCategoryFilter(value)}
            >
              <SelectTrigger className='w-[180px]'>
                <SelectValue placeholder='Category' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Categories</SelectItem>
                {menuCategories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={statusFilter}
              onValueChange={(value) =>
                setStatusFilter(value as MenuItemStatus | 'all')
              }
            >
              <SelectTrigger className='w-[180px]'>
                <SelectValue placeholder='Status' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Statuses</SelectItem>
                <SelectItem value='Available'>Available</SelectItem>
                <SelectItem value='Unavailable'>Unavailable</SelectItem>
                <SelectItem value='Seasonal'>Seasonal</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={dietaryFilter}
              onValueChange={(value) => setDietaryFilter(value)}
            >
              <SelectTrigger className='w-[180px]'>
                <SelectValue placeholder='Dietary' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Items</SelectItem>
                <SelectItem value='vegetarian'>Vegetarian</SelectItem>
                <SelectItem value='vegan'>Vegan</SelectItem>
                <SelectItem value='gluten-free'>Gluten-Free</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Dietary</TableHead>
              <TableHead className='text-right'>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedMenuItems.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={6}
                  className='text-center h-32 text-muted-foreground'
                >
                  No menu items found
                </TableCell>
              </TableRow>
            ) : (
              sortedMenuItems.map((menuItem) => (
                <TableRow key={menuItem.id}>
                  <TableCell className='font-medium'>
                    <div>
                      <div>{menuItem.name}</div>
                      <div className='text-xs text-muted-foreground line-clamp-1'>
                        {menuItem.description}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{getCategoryName(menuItem.categoryId)}</TableCell>
                  <TableCell>{formatPrice(menuItem.price)}</TableCell>
                  <TableCell>
                    <Badge
                      variant={getStatusBadgeVariant(menuItem.status) as any}
                    >
                      {menuItem.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className='flex gap-1'>
                      {menuItem.isVegetarian && (
                        <Badge
                          variant='outline'
                          className='bg-green-50 text-green-700 border-green-200'
                        >
                          V
                        </Badge>
                      )}
                      {menuItem.isVegan && (
                        <Badge
                          variant='outline'
                          className='bg-green-50 text-green-700 border-green-200'
                        >
                          VG
                        </Badge>
                      )}
                      {menuItem.isGlutenFree && (
                        <Badge
                          variant='outline'
                          className='bg-yellow-50 text-yellow-700 border-yellow-200'
                        >
                          GF
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className='text-right'>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant='ghost' size='icon'>
                          <MoreVertical className='h-4 w-4' />
                          <span className='sr-only'>Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align='end' className='bg-popover'>
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => onEditMenuItem(menuItem)}
                        >
                          <Pencil className='mr-2 h-4 w-4' />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className='text-destructive focus:text-destructive'
                          onClick={() => setMenuItemToDelete(menuItem)}
                        >
                          <Trash2 className='mr-2 h-4 w-4' />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmation
        isOpen={!!menuItemToDelete}
        onClose={() => setMenuItemToDelete(null)}
        onConfirm={() => {
          if (menuItemToDelete) {
            onDeleteMenuItem(menuItemToDelete.id);
            toast.success(
              `${menuItemToDelete.name} has been removed from the menu.`
            );
            setMenuItemToDelete(null);
          }
        }}
        title='Delete Menu Item'
        description={`Are you sure you want to delete "${menuItemToDelete?.name}" from the menu? This action cannot be undone.`}
      />
    </Card>
  );
};

export default MenuList;
