import { useEffect } from 'react';
import { MenuItem, MenuCategory, MenuItemStatus } from '@/types/menu';
import { Ingredient } from '@/types/ingredient';
import { useIngredientsStore } from '@/store/store';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';

interface MenuFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (menuItem: MenuItem) => void;
  menuItem?: MenuItem;
  menuCategories: MenuCategory[];
}

// Create a schema for form validation
const formSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  description: z.string().min(5, 'Description must be at least 5 characters'),
  price: z.coerce.number().min(0, 'Price must be a positive number'),
  categoryId: z.string().min(1, 'Category is required'),
  status: z.enum(['Available', 'Unavailable', 'Seasonal']),
  selectedIngredientIds: z
    .array(z.string())
    .min(1, 'At least one ingredient is required'),
  allergens: z.string(),
  calories: z.coerce.number().optional(),
  protein: z.coerce.number().optional(),
  carbs: z.coerce.number().optional(),
  fat: z.coerce.number().optional(),
  isVegetarian: z.boolean().default(false),
  isVegan: z.boolean().default(false),
  isGlutenFree: z.boolean().default(false),
  preparationTime: z.coerce
    .number()
    .min(0, 'Preparation time must be a positive number'),
});

type FormValues = z.infer<typeof formSchema>;

const MenuForm = ({
  isOpen,
  onClose,
  onSave,
  menuItem,
  menuCategories,
}: MenuFormProps) => {
  const isEditing = !!menuItem;

  const { ingredients } = useIngredientsStore();

  // Convert ingredient names to IDs for the form
  const getIngredientIdsByNames = (names: string[]): string[] => {
    return ingredients
      .filter((ingredient) => names.includes(ingredient.name))
      .map((ingredient) => ingredient.id);
  };

  // Get ingredient names from IDs
  const getIngredientNamesByIds = (ids: string[]): string[] => {
    return ingredients
      .filter((ingredient) => ids.includes(ingredient.id))
      .map((ingredient) => ingredient.name);
  };

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: menuItem?.name || '',
      description: menuItem?.description || '',
      price: menuItem?.price || 0,
      categoryId: menuItem?.categoryId || '',
      status: menuItem?.status || 'Available',
      selectedIngredientIds: menuItem
        ? getIngredientIdsByNames(menuItem.ingredients)
        : [],
      allergens: menuItem?.allergens.join(', ') || '',
      calories: menuItem?.nutritionalInfo?.calories || undefined,
      protein: menuItem?.nutritionalInfo?.protein || undefined,
      carbs: menuItem?.nutritionalInfo?.carbs || undefined,
      fat: menuItem?.nutritionalInfo?.fat || undefined,
      isVegetarian: menuItem?.isVegetarian || false,
      isVegan: menuItem?.isVegan || false,
      isGlutenFree: menuItem?.isGlutenFree || false,
      preparationTime: menuItem?.preparationTime || 0,
    },
  });

  useEffect(() => {
    if (menuItem) {
      form.reset({
        name: menuItem.name,
        description: menuItem.description,
        price: menuItem.price,
        categoryId: menuItem.categoryId,
        status: menuItem.status,
        selectedIngredientIds: getIngredientIdsByNames(menuItem.ingredients),
        allergens: menuItem.allergens.join(', '),
        calories: menuItem.nutritionalInfo?.calories,
        protein: menuItem.nutritionalInfo?.protein,
        carbs: menuItem.nutritionalInfo?.carbs,
        fat: menuItem.nutritionalInfo?.fat,
        isVegetarian: menuItem.isVegetarian,
        isVegan: menuItem.isVegan,
        isGlutenFree: menuItem.isGlutenFree,
        preparationTime: menuItem.preparationTime,
      });
    } else {
      form.reset({
        name: '',
        description: '',
        price: 0,
        categoryId: '',
        status: 'Available',
        selectedIngredientIds: [],
        allergens: '',
        calories: undefined,
        protein: undefined,
        carbs: undefined,
        fat: undefined,
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: false,
        preparationTime: 0,
      });
    }
  }, [menuItem, form, ingredients]);

  const handleSubmit = (values: FormValues) => {
    // Find the selected category
    const selectedCategory = menuCategories.find(
      (c) => c.id === values.categoryId
    );

    if (!selectedCategory && values.categoryId) {
      alert('Selected category not found!');
      return;
    }

    const menuItemData: MenuItem = {
      id: menuItem?.id || crypto.randomUUID(),
      name: values.name,
      description: values.description,
      price: values.price,
      categoryId: values.categoryId,
      category: selectedCategory?.name || '',
      ingredients: getIngredientNamesByIds(values.selectedIngredientIds),
      allergens: values.allergens
        .split(',')
        .map((a) => a.trim())
        .filter(Boolean),
      nutritionalInfo: {
        calories: values.calories,
        protein: values.protein,
        carbs: values.carbs,
        fat: values.fat,
      },
      status: values.status as MenuItemStatus,
      isVegetarian: values.isVegetarian,
      isVegan: values.isVegan,
      isGlutenFree: values.isGlutenFree,
      preparationTime: values.preparationTime,
    };

    onSave(menuItemData);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className='sm:max-w-[600px]'>
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Menu Item' : 'Add New Menu Item'}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update menu item information.'
              : 'Fill in the details to add a new menu item.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className='space-y-6'
          >
            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='name'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder='Grilled Salmon' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='price'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Price ($)</FormLabel>
                    <FormControl>
                      <Input type='number' step='0.01' min='0' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Brief description of the menu item'
                      className='resize-none'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='categoryId'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select a category' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {menuCategories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='status'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select status' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value='Available'>Available</SelectItem>
                        <SelectItem value='Unavailable'>Unavailable</SelectItem>
                        <SelectItem value='Seasonal'>Seasonal</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='selectedIngredientIds'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ingredients</FormLabel>
                    <div className='space-y-2'>
                      <div className='flex flex-wrap gap-2 mb-2'>
                        {field.value.map((id) => {
                          const ingredient = ingredients.find(
                            (i) => i.id === id
                          );
                          return ingredient ? (
                            <Badge
                              key={id}
                              variant='secondary'
                              className='flex items-center gap-1'
                            >
                              {ingredient.name}
                              <button
                                type='button'
                                onClick={() => {
                                  field.onChange(
                                    field.value.filter((i) => i !== id)
                                  );
                                }}
                                className='text-muted-foreground hover:text-foreground rounded-full'
                              >
                                <X className='h-3 w-3' />
                              </button>
                            </Badge>
                          ) : null;
                        })}
                      </div>
                      <Select
                        onValueChange={(value) => {
                          if (!field.value.includes(value)) {
                            field.onChange([...field.value, value]);
                          }
                        }}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select ingredients' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {ingredients
                            .filter((i) => !field.value.includes(i.id))
                            .map((ingredient) => (
                              <SelectItem
                                key={ingredient.id}
                                value={ingredient.id}
                              >
                                {ingredient.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <FormDescription>
                      Select ingredients from the list
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='allergens'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Allergens</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Comma-separated list of allergens'
                        className='resize-none'
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Separate allergens with commas
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div>
              <h3 className='text-sm font-medium mb-2'>
                Nutritional Information
              </h3>
              <div className='grid grid-cols-4 gap-4'>
                <FormField
                  control={form.control}
                  name='calories'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Calories</FormLabel>
                      <FormControl>
                        <Input type='number' min='0' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='protein'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Protein (g)</FormLabel>
                      <FormControl>
                        <Input type='number' min='0' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='carbs'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Carbs (g)</FormLabel>
                      <FormControl>
                        <Input type='number' min='0' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='fat'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Fat (g)</FormLabel>
                      <FormControl>
                        <Input type='number' min='0' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className='grid grid-cols-2 gap-4'>
              <div>
                <h3 className='text-sm font-medium mb-2'>
                  Dietary Information
                </h3>
                <div className='space-y-2'>
                  <FormField
                    control={form.control}
                    name='isVegetarian'
                    render={({ field }) => (
                      <FormItem className='flex flex-row items-start space-x-3 space-y-0'>
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className='space-y-1 leading-none'>
                          <FormLabel>Vegetarian</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='isVegan'
                    render={({ field }) => (
                      <FormItem className='flex flex-row items-start space-x-3 space-y-0'>
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className='space-y-1 leading-none'>
                          <FormLabel>Vegan</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='isGlutenFree'
                    render={({ field }) => (
                      <FormItem className='flex flex-row items-start space-x-3 space-y-0'>
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className='space-y-1 leading-none'>
                          <FormLabel>Gluten-Free</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <FormField
                control={form.control}
                name='preparationTime'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preparation Time (minutes)</FormLabel>
                    <FormControl>
                      <Input type='number' min='0' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button type='button' variant='outline' onClick={onClose}>
                Cancel
              </Button>
              <Button type='submit'>{isEditing ? 'Update' : 'Create'}</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default MenuForm;
