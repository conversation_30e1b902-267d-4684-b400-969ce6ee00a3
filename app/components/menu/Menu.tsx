import { useState } from 'react';
import { MenuItem, MenuCategory } from '@/types/menu';
import { useMenuItemsStore, useMenuCategoriesStore } from '@/store/store';
import MenuList from '@/components/menu/MenuList';
import MenuForm from '@/components/menu/MenuForm';
import MenuCategoryList from '@/components/menu/MenuCategoryList';
import MenuCategoryForm from '@/components/menu/MenuCategoryForm';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const MenuPage = () => {
  const { menuItems, addMenuItem, deleteMenuItem, updateMenuItem } =
    useMenuItemsStore();
  const {
    menuCategories,
    addMenuCategory,
    deleteMenuCategory,
    updateMenuCategory,
  } = useMenuCategoriesStore();

  const [isMenuItemFormOpen, setIsMenuItemFormOpen] = useState(false);
  const [isMenuCategoryFormOpen, setIsMenuCategoryFormOpen] = useState(false);
  const [currentMenuItem, setCurrentMenuItem] = useState<MenuItem | undefined>(
    undefined
  );
  const [currentMenuCategory, setCurrentMenuCategory] = useState<
    MenuCategory | undefined
  >(undefined);

  // Menu Item handlers
  const handleAddMenuItem = () => {
    setCurrentMenuItem(undefined);
    setIsMenuItemFormOpen(true);
  };

  const handleEditMenuItem = (menuItem: MenuItem) => {
    setCurrentMenuItem(menuItem);
    setIsMenuItemFormOpen(true);
  };

  const handleSaveMenuItem = (menuItem: MenuItem) => {
    const index = menuItems.findIndex((m) => m.id === menuItem.id);
    if (index >= 0) {
      updateMenuItem(menuItem.id, menuItem);
    } else {
      addMenuItem(menuItem);
    }
  };

  const handleDeleteMenuItem = (menuItemId: string) => {
    deleteMenuItem(menuItemId);
  };

  // Menu Category handlers
  const handleAddMenuCategory = () => {
    setCurrentMenuCategory(undefined);
    setIsMenuCategoryFormOpen(true);
  };

  const handleEditMenuCategory = (menuCategory: MenuCategory) => {
    setCurrentMenuCategory(menuCategory);
    setIsMenuCategoryFormOpen(true);
  };

  const handleSaveMenuCategory = (menuCategory: MenuCategory) => {
    const index = menuCategories.findIndex((c) => c.id === menuCategory.id);
    if (index >= 0) {
      updateMenuCategory(menuCategory.id, menuCategory);
    } else {
      addMenuCategory(menuCategory);
    }
  };

  const handleDeleteMenuCategory = (menuCategoryId: string) => {
    // Check if any menu items are using this category
    const itemsUsingCategory = menuItems.filter(
      (item) => item.categoryId === menuCategoryId
    );

    if (itemsUsingCategory.length > 0) {
      alert(
        `Cannot delete category: This category is used by ${
          itemsUsingCategory.length
        } menu item${
          itemsUsingCategory.length > 1 ? 's' : ''
        }. Please reassign or delete these items first.`
      );
      return;
    }

    deleteMenuCategory(menuCategoryId);
  };

  return (
    <div className='space-y-6'>
      <h1 className='text-3xl font-bold'>Menu Management</h1>
      <p className='text-muted-foreground'>
        Create and manage your restaurant's menu items and categories.
      </p>

      <Tabs defaultValue='items' className='w-full'>
        <TabsList>
          <TabsTrigger value='items'>Menu Items</TabsTrigger>
          <TabsTrigger value='categories'>Categories</TabsTrigger>
        </TabsList>

        <TabsContent value='items' className='mt-6'>
          <MenuList
            menuItems={menuItems}
            menuCategories={menuCategories}
            onAddMenuItem={handleAddMenuItem}
            onEditMenuItem={handleEditMenuItem}
            onDeleteMenuItem={handleDeleteMenuItem}
          />
        </TabsContent>

        <TabsContent value='categories' className='mt-6'>
          <MenuCategoryList
            menuCategories={menuCategories}
            menuItems={menuItems}
            onAddMenuCategory={handleAddMenuCategory}
            onEditMenuCategory={handleEditMenuCategory}
            onDeleteMenuCategory={handleDeleteMenuCategory}
          />
        </TabsContent>
      </Tabs>

      {isMenuItemFormOpen && (
        <MenuForm
          isOpen={isMenuItemFormOpen}
          onClose={() => setIsMenuItemFormOpen(false)}
          onSave={handleSaveMenuItem}
          menuItem={currentMenuItem}
          menuCategories={menuCategories}
        />
      )}

      {isMenuCategoryFormOpen && (
        <MenuCategoryForm
          isOpen={isMenuCategoryFormOpen}
          onClose={() => setIsMenuCategoryFormOpen(false)}
          onSave={handleSaveMenuCategory}
          menuCategory={currentMenuCategory}
        />
      )}
    </div>
  );
};

export default MenuPage;
