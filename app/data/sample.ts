import { Department } from '@/types/department';
import { Employee } from '@/types/employee';
import { Role } from '@/types/role';
import { MenuItem, MenuCategory } from '@/types/menu';
import { Ingredient, IngredientCategory } from '@/types/ingredient';
import { Space } from '@/types/space'; // Import Space type
import {
  Order,
  OrderItem,
  OrderStatus,
  OrderType,
  PaymentMethod,
  PaymentStatus,
} from '@/types/order';
import { Table } from '@/types/table';
import { Supplier } from '@/types/supplier';

// Initial sample data
export const initialRoles: Role[] = [
  {
    id: '1',
    name: 'Head Chef',
    department: 'Kitchen',
    description: 'Manages kitchen operations and menu development',
    permissions: ['Manage Menu', 'Manage Inventory', 'View Schedule'],
  },
  {
    id: '2',
    name: 'Sous Chef',
    department: 'Kitchen',
    description: 'Assists head chef in kitchen operations',
    permissions: ['View Menu', 'View Inventory', 'View Schedule'],
  },
  {
    id: '3',
    name: 'Server',
    department: 'Front of House',
    description: 'Provides customer service and takes orders',
    permissions: ['View Menu', 'View Schedule'],
  },
  {
    id: '4',
    name: 'Manager',
    department: 'Management',
    description: 'Oversees restaurant operations',
    permissions: [
      'Manage Employees',
      'Manage Schedule',
      'Manage Reports',
      'View Inventory',
      'Admin Access',
    ],
  },
];

// Initial sample spaces
export const initialSpaces: Space[] = [
  {
    id: 'space-1',
    name: 'Main Dining Hall',
    description: 'The central area of the restaurant, spacious with natural light.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'space-2',
    name: 'Patio Seating',
    description: 'Outdoor seating area, perfect for sunny days.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'bar-1',
    name: 'Bar Area',
    description: 'Cozy bar with high-top tables and full drink service.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'private-room-1',
    name: 'Private Dining Room A',
    description: 'A secluded room for private events and parties, fits up to 20 guests.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Initial sample tables
export const initialTables: Table[] = [
  {
    id: 'table-1',
    name: 'T1',
    capacity: 4,
    status: 'available',
    spaceId: 'space-1',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'table-2',
    name: 'T2',
    capacity: 2,
    status: 'occupied',
    spaceId: 'space-1',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'table-3',
    name: 'T3',
    capacity: 6,
    status: 'reserved',
    spaceId: 'space-2',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'table-4',
    name: 'T4',
    capacity: 4,
    status: 'out-of-service',
    spaceId: 'space-1',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'table-5',
    name: 'T5',
    capacity: 8,
    status: 'available',
    spaceId: 'space-2',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'table-6',
    name: 'B1',
    capacity: 2,
    status: 'available',
    spaceId: 'bar-1',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'table-7',
    name: 'P1',
    capacity: 10,
    status: 'reserved',
    spaceId: 'space-2',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

export const initialEmployees: Employee[] = [
  {
    id: '1',
    name: 'Michael Chen',
    email: '<EMAIL>',
    phone: '(*************',
    status: 'Active',
    role: initialRoles[0],
  },
  {
    id: '2',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '(*************',
    status: 'Active',
    role: initialRoles[1],
  },
  {
    id: '3',
    name: 'David Smith',
    email: '<EMAIL>',
    phone: '(*************',
    status: 'Active',
    role: initialRoles[2],
  },
  {
    id: '4',
    name: 'Jessica Rivera',
    email: '<EMAIL>',
    phone: '(*************',
    status: 'Inactive',
    role: initialRoles[2],
  },
];

// Initial sample departments
export const initialDepartments: Department[] = [
  {
    id: '1',
    name: 'Kitchen',
    description: 'Kitchen staff and operations',
  },
  {
    id: '2',
    name: 'Front of House',
    description: 'Customer-facing staff and operations',
  },
  {
    id: '3',
    name: 'Management',
    description: 'Management and administration',
  },
  {
    id: '4',
    name: 'Bar',
    description: 'Bar staff and operations',
  },
];

// Initial sample menu categories
export const initialMenuCategories: MenuCategory[] = [
  {
    id: '1',
    name: 'Appetizers',
    description: 'Starters and small plates',
    order: 1,
  },
  {
    id: '2',
    name: 'Main Courses',
    description: 'Entrees and main dishes',
    order: 2,
  },
  {
    id: '3',
    name: 'Desserts',
    description: 'Sweet treats to finish your meal',
    order: 3,
  },
  {
    id: '4',
    name: 'Beverages',
    description: 'Drinks and refreshments',
    order: 4,
    // This is now a parent category
  },
  {
    id: 'bev-hot', // New ID for Hot Drinks
    name: 'Hot Drinks',
    description: 'Warm beverages',
    order: 1, // Order within Beverages
    parentId: '4',
  },
  {
    id: 'bev-coffee', // New ID for Coffee
    name: 'Coffee',
    description: 'Various coffee options',
    order: 1, // Order within Hot Drinks
    parentId: 'bev-hot',
  },
  {
    id: 'bev-tea', // New ID for Tea
    name: 'Tea',
    description: 'Assorted teas',
    order: 2, // Order within Hot Drinks
    parentId: 'bev-hot',
  },
  {
    id: 'bev-cold', // New ID for Cold Drinks
    name: 'Cold Drinks',
    description: 'Chilled beverages',
    order: 2, // Order within Beverages
    parentId: '4',
  },
  {
    id: '5',
    name: 'Sides',
    description: 'Side dishes and accompaniments',
    order: 5, // This will be after Beverages and its subcategories in overall display if sorted by main order then sub-order
  },
];

// Initial sample menu items
export const initialMenuItems: MenuItem[] = [
  {
    id: '1',
    name: 'Crispy Calamari',
    description: 'Lightly fried calamari served with lemon aioli',
    price: 12.99,
    categoryId: '1',
    category: 'Appetizers',
    ingredients: ['Calamari', 'Flour', 'Lemon', 'Garlic', 'Mayonnaise'],
    allergens: ['Gluten', 'Seafood', 'Eggs'],
    nutritionalInfo: {
      calories: 320,
      protein: 15,
      carbs: 28,
      fat: 18,
    },
    status: 'Available',
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
    preparationTime: 15,
  },
  {
    id: '2',
    name: 'Grilled Salmon',
    description:
      'Fresh Atlantic salmon with lemon herb butter, served with seasonal vegetables',
    price: 24.99,
    categoryId: '2',
    category: 'Main Courses',
    ingredients: ['Salmon', 'Butter', 'Lemon', 'Herbs', 'Seasonal Vegetables'],
    allergens: ['Fish', 'Dairy'],
    nutritionalInfo: {
      calories: 450,
      protein: 38,
      carbs: 12,
      fat: 28,
    },
    status: 'Available',
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: true,
    preparationTime: 25,
  },
  {
    id: '3',
    name: 'Chocolate Lava Cake',
    description:
      'Warm chocolate cake with a molten center, served with vanilla ice cream',
    price: 9.99,
    categoryId: '3',
    category: 'Desserts',
    ingredients: [
      'Chocolate',
      'Flour',
      'Sugar',
      'Eggs',
      'Butter',
      'Vanilla Ice Cream',
    ],
    allergens: ['Gluten', 'Dairy', 'Eggs'],
    nutritionalInfo: {
      calories: 580,
      protein: 8,
      carbs: 65,
      fat: 32,
    },
    status: 'Available',
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false,
    preparationTime: 20,
  },
  {
    id: '4',
    name: 'Craft IPA',
    description: 'Locally brewed IPA with citrus notes',
    price: 7.99,
    categoryId: '4',
    category: 'Beverages',
    ingredients: ['Water', 'Barley', 'Hops', 'Yeast'],
    allergens: ['Gluten'],
    nutritionalInfo: {
      calories: 220,
      carbs: 18,
    },
    status: 'Available',
    isVegetarian: true,
    isVegan: true,
    isGlutenFree: false,
    preparationTime: 2,
  },
  {
    id: '5',
    name: 'Truffle Fries',
    description: 'Hand-cut fries tossed with truffle oil and parmesan',
    price: 8.99,
    categoryId: '5',
    category: 'Sides',
    ingredients: ['Potatoes', 'Truffle Oil', 'Parmesan', 'Salt', 'Parsley'],
    allergens: ['Dairy'],
    nutritionalInfo: {
      calories: 380,
      protein: 6,
      carbs: 45,
      fat: 22,
    },
    status: 'Available',
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: true,
    preparationTime: 12,
  },
  {
    id: '6',
    name: 'Vegetable Stir Fry',
    description: 'Seasonal vegetables stir-fried with tofu in a savory sauce',
    price: 16.99,
    categoryId: '2',
    category: 'Main Courses',
    ingredients: [
      'Tofu',
      'Bell Peppers',
      'Broccoli',
      'Carrots',
      'Soy Sauce',
      'Ginger',
      'Garlic',
    ],
    allergens: ['Soy'],
    nutritionalInfo: {
      calories: 320,
      protein: 18,
      carbs: 32,
      fat: 14,
    },
    status: 'Available',
    isVegetarian: true,
    isVegan: true,
    isGlutenFree: true,
    preparationTime: 18,
  },
];

// Initial sample shifts
// Helper function to create order items
const createOrderItem = (
  id: string,
  menuItemId: string,
  quantity: number,
  price: number,
  notes?: string
): OrderItem => ({
  id,
  menuItemId,
  quantity,
  price,
  notes,
  modifiers: [],
});

// Helper function to calculate order totals
const calculateOrderTotals = (items: OrderItem[]) => {
  const subtotal = items.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );
  const tax = subtotal * 0.0825; // 8.25% tax rate
  const total = subtotal + tax;
  return { subtotal, tax, total };
};

// Initial sample ingredient categories
export const initialIngredientCategories: IngredientCategory[] = [
  {
    id: '1',
    name: 'Protein',
    description: 'Meat, fish, and other protein sources',
  },
  {
    id: '2',
    name: 'Vegetable',
    description: 'Fresh and cooked vegetables',
  },
  {
    id: '3',
    name: 'Fruit',
    description: 'Fresh and dried fruits',
  },
  {
    id: '4',
    name: 'Dairy',
    description: 'Milk, cheese, and other dairy products',
  },
  {
    id: '5',
    name: 'Grain',
    description: 'Wheat, rice, and other grains',
  },
  {
    id: '6',
    name: 'Spice',
    description: 'Herbs, spices, and seasonings',
  },
  {
    id: '7',
    name: 'Sauce',
    description: 'Condiments and sauces',
  },
  {
    id: '8',
    name: 'Other',
    description: 'Miscellaneous ingredients',
  },
];

// Initial sample ingredients
export const initialIngredients: Ingredient[] = [
  {
    id: '1',
    name: 'Calamari',
    description: 'Fresh squid rings',
    unit: 'kg',
    categoryId: '1',
    inStock: true,
    allergen: true,
    allergenType: 'Seafood',
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '2',
    name: 'Flour',
    description: 'All-purpose wheat flour',
    unit: 'kg',
    categoryId: '5',
    inStock: true,
    allergen: true,
    allergenType: 'Gluten',
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '3',
    name: 'Lemon',
    description: 'Fresh lemons',
    unit: 'piece',
    categoryId: '3',
    inStock: true,
    allergen: false,
    stockLevel: 100,
    unitOfMeasure: 'piece',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '4',
    name: 'Garlic',
    description: 'Fresh garlic cloves',
    unit: 'kg',
    categoryId: '2',
    inStock: true,
    allergen: false,
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '5',
    name: 'Mayonnaise',
    description: 'Creamy mayonnaise',
    unit: 'liter',
    categoryId: '7',
    inStock: true,
    allergen: true,
    allergenType: 'Eggs',
    stockLevel: 100,
    unitOfMeasure: 'liter',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '6',
    name: 'Salmon',
    description: 'Fresh Atlantic salmon fillets',
    unit: 'kg',
    categoryId: '1',
    inStock: true,
    allergen: true,
    allergenType: 'Fish',
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '2',
  },
  {
    id: '7',
    name: 'Butter',
    description: 'Unsalted butter',
    unit: 'kg',
    categoryId: '4',
    inStock: true,
    allergen: true,
    allergenType: 'Dairy',
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '2',
  },
  {
    id: '8',
    name: 'Seasonal Vegetables',
    description: 'Mix of seasonal vegetables',
    unit: 'kg',
    categoryId: '2',
    inStock: true,
    allergen: false,
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '9',
    name: 'Chocolate',
    description: 'Dark chocolate',
    unit: 'kg',
    categoryId: '8',
    inStock: true,
    allergen: false,
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '2',
  },
  {
    id: '10',
    name: 'Sugar',
    description: 'Granulated sugar',
    unit: 'kg',
    categoryId: '8',
    inStock: true,
    allergen: false,
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '2',
  },
  {
    id: '11',
    name: 'Eggs',
    description: 'Fresh chicken eggs',
    unit: 'dozen',
    categoryId: '1',
    inStock: true,
    allergen: true,
    allergenType: 'Eggs',
    stockLevel: 100,
    unitOfMeasure: 'dozen',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '12',
    name: 'Vanilla Ice Cream',
    description: 'Premium vanilla ice cream',
    unit: 'liter',
    categoryId: '4',
    inStock: true,
    allergen: true,
    allergenType: 'Dairy',
    stockLevel: 100,
    unitOfMeasure: 'liter',
    lowStockThreshold: 20,
    supplierId: '2',
  },
  {
    id: '13',
    name: 'Barley',
    description: 'Malted barley',
    unit: 'kg',
    categoryId: '5',
    inStock: true,
    allergen: true,
    allergenType: 'Gluten',
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '14',
    name: 'Hops',
    description: 'Aromatic hops',
    unit: 'kg',
    categoryId: '8',
    inStock: true,
    allergen: false,
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '15',
    name: 'Potatoes',
    description: 'Russet potatoes',
    unit: 'kg',
    categoryId: '2',
    inStock: true,
    allergen: false,
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '16',
    name: 'Truffle Oil',
    description: 'Premium truffle-infused oil',
    unit: 'liter',
    categoryId: '8',
    inStock: true,
    allergen: false,
    stockLevel: 100,
    unitOfMeasure: 'liter',
    lowStockThreshold: 20,
    supplierId: '2',
  },
  {
    id: '17',
    name: 'Parmesan',
    description: 'Aged Parmesan cheese',
    unit: 'kg',
    categoryId: '4',
    inStock: true,
    allergen: true,
    allergenType: 'Dairy',
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '2',
  },
  {
    id: '18',
    name: 'Salt',
    description: 'Sea salt',
    unit: 'kg',
    categoryId: '6',
    inStock: true,
    allergen: false,
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '19',
    name: 'Parsley',
    description: 'Fresh parsley',
    unit: 'kg',
    categoryId: '6',
    inStock: true,
    allergen: false,
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '20',
    name: 'Tofu',
    description: 'Firm tofu',
    unit: 'kg',
    categoryId: '1',
    inStock: true,
    allergen: true,
    allergenType: 'Soy',
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '21',
    name: 'Bell Peppers',
    description: 'Assorted bell peppers',
    unit: 'kg',
    categoryId: '2',
    inStock: true,
    allergen: false,
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '22',
    name: 'Broccoli',
    description: 'Fresh broccoli',
    unit: 'kg',
    categoryId: '2',
    inStock: true,
    allergen: false,
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '23',
    name: 'Carrots',
    description: 'Fresh carrots',
    unit: 'kg',
    categoryId: '2',
    inStock: true,
    allergen: false,
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '1',
  },
  {
    id: '24',
    name: 'Soy Sauce',
    description: 'Traditional soy sauce',
    unit: 'liter',
    categoryId: '7',
    inStock: true,
    allergen: true,
    allergenType: 'Soy',
    stockLevel: 100,
    unitOfMeasure: 'liter',
    lowStockThreshold: 20,
    supplierId: '2',
  },
  {
    id: '25',
    name: 'Ginger',
    description: 'Fresh ginger root',
    unit: 'kg',
    categoryId: '6',
    inStock: true,
    allergen: false,
    stockLevel: 100,
    unitOfMeasure: 'kg',
    lowStockThreshold: 20,
    supplierId: '1',
  },
];

// Initial sample orders
export const initialOrders: Order[] = [
  {
    id: '1',
    orderNumber: 'ORD-001',
    tableNumber: '12', // This might become redundant or used for display if tableName is not available
    tableId: 'table-2', // Corresponds to T2
    tableName: 'T2',
    customerName: 'John Doe',
    customerPhone: '(*************',
    orderType: 'Dine-in',
    status: 'Completed',
    items: [
      createOrderItem('1-1', '1', 2, 12.99, 'Extra sauce on the side'),
      createOrderItem('1-2', '5', 1, 8.99),
    ],
    subtotal: 34.97,
    tax: 2.88,
    tip: 7.0,
    total: 44.85,
    paymentStatus: 'Paid',
    paymentMethod: 'Credit Card',
    createdAt: (() => {
      const date = new Date();
      date.setHours(date.getHours() - 3);
      return date.toISOString();
    })(),
    updatedAt: (() => {
      const date = new Date();
      date.setHours(date.getHours() - 2);
      return date.toISOString();
    })(),
    completedAt: (() => {
      const date = new Date();
      date.setHours(date.getHours() - 2);
      return date.toISOString();
    })(),
    assignedTo: '3', // David Smith (Server)
    notes: 'Customer celebrating birthday',
  },
  {
    id: '2',
    orderNumber: 'ORD-002',
    customerName: 'Jane Smith',
    customerPhone: '(*************',
    customerEmail: '<EMAIL>',
    orderType: 'Takeout',
    status: 'Ready',
    items: [
      createOrderItem('2-1', '2', 1, 24.99),
      createOrderItem('2-2', '3', 2, 9.99),
      createOrderItem('2-3', '4', 2, 7.99),
    ],
    subtotal: 60.95,
    tax: 5.03,
    total: 65.98,
    paymentStatus: 'Paid',
    paymentMethod: 'Credit Card',
    createdAt: (() => {
      const date = new Date();
      date.setMinutes(date.getMinutes() - 45);
      return date.toISOString();
    })(),
    updatedAt: (() => {
      const date = new Date();
      date.setMinutes(date.getMinutes() - 10);
      return date.toISOString();
    })(),
    assignedTo: '2', // Sarah Johnson (Sous Chef)
    notes: 'No utensils needed',
  },
  {
    id: '3',
    orderNumber: 'ORD-003',
    customerName: 'Robert Johnson',
    customerPhone: '(*************',
    customerEmail: '<EMAIL>',
    orderType: 'Delivery',
    status: 'In Progress',
    items: [
      createOrderItem('3-1', '6', 1, 16.99),
      createOrderItem('3-2', '5', 1, 8.99),
      createOrderItem('3-3', '4', 1, 7.99),
    ],
    subtotal: 33.97,
    tax: 2.8,
    total: 36.77,
    paymentStatus: 'Paid',
    paymentMethod: 'Mobile Payment',
    createdAt: (() => {
      const date = new Date();
      date.setMinutes(date.getMinutes() - 20);
      return date.toISOString();
    })(),
    updatedAt: (() => {
      const date = new Date();
      date.setMinutes(date.getMinutes() - 15);
      return date.toISOString();
    })(),
    assignedTo: '1', // Michael Chen (Head Chef)
    deliveryAddress: '123 Main St, Apt 4B, Anytown, USA',
    deliveryInstructions: 'Leave at door, call upon arrival',
  },
  {
    id: '4',
    orderNumber: 'ORD-004',
    tableNumber: '8', // This might become redundant
    tableId: 'table-5', // Corresponds to T5
    tableName: 'T5',
    customerName: 'Maria Garcia',
    orderType: 'Dine-in',
    status: 'Pending',
    items: [
      createOrderItem('4-1', '1', 1, 12.99),
      createOrderItem('4-2', '2', 1, 24.99),
    ],
    subtotal: 37.98,
    tax: 3.13,
    total: 41.11,
    paymentStatus: 'Unpaid',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    assignedTo: '3', // David Smith (Server)
  },
  {
    id: '5',
    orderNumber: 'ORD-005',
    customerName: 'Corporate Event',
    customerPhone: '(*************',
    customerEmail: '<EMAIL>',
    orderType: 'Catering',
    status: 'Pending',
    items: [
      createOrderItem('5-1', '1', 10, 12.99),
      createOrderItem('5-2', '5', 10, 8.99),
      createOrderItem('5-3', '6', 10, 16.99),
      createOrderItem('5-4', '3', 10, 9.99),
    ],
    subtotal: 489.6,
    tax: 40.39,
    discount: 50.0,
    total: 479.99,
    paymentStatus: 'Partial',
    paymentMethod: 'Credit Card',
    createdAt: (() => {
      const date = new Date();
      date.setDate(date.getDate() + 5); // 5 days in the future
      return date.toISOString();
    })(),
    updatedAt: (() => {
      const date = new Date();
      date.setDate(date.getDate() + 5);
      return date.toISOString();
    })(),
    assignedTo: '1', // Michael Chen (Head Chef)
    deliveryAddress: 'ACME Corp, 456 Business Ave, Suite 200, Anytown, USA',
    deliveryInstructions: 'Deliver to reception, setup in conference room A',
    notes: 'Deposit of $200 paid, balance due upon delivery',
  },
];

export const initialSuppliers: Supplier[] = [
  {
    id: '1',
    name: 'Fresh Produce Inc.',
    contactPerson: 'John Appleseed',
    phone: '(*************',
    email: '<EMAIL>',
    address: '123 Produce Lane, Farmville',
    productsSupplied: ['Vegetables', 'Fruits'],
  },
  {
    id: '2',
    name: 'Quality Meats Co.',
    contactPerson: 'Jane Doe',
    phone: '(*************',
    email: '<EMAIL>',
    address: '456 Meat Street, Butcherburg',
    productsSupplied: ['Beef', 'Poultry', 'Pork'],
  },
];
