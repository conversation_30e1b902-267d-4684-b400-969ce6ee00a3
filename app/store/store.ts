import { Department } from './../types/department';
import { Employee } from '@/types/employee';
import { Role } from '@/types/role';
import { MenuItem, MenuCategory } from '@/types/menu';
import { Order, OrderStatus, PaymentStatus } from '@/types/order';
import { Ingredient, IngredientCategory } from '@/types/ingredient';
import { Table } from '@/types/table'; // Import Table type
import { Space } from '@/types/space'; // Import Space type
import { Supplier } from '@/types/supplier';
import { create } from 'zustand';

import {
  initialEmployees,
  initialRoles,
  initialDepartments,
  initialMenuItems,
  initialMenuCategories,
  initialOrders,
  initialIngredients,
  initialIngredientCategories,
  initialTables, // Import initialTables
  initialSpaces, // Import initialSpaces
  initialSuppliers,
} from '@/data/sample';

type RolesStore = {
  roles: Role[];
  setRoles: (roles: Role[]) => void;
  addRole: (role: Role) => void;
  deleteRole: (id: string) => void;
  updateRole: (id: string, role: Role) => void;
};

type EmployeesStore = {
  employees: Employee[];
  setEmployees: (employees: Employee[]) => void;
  addEmployee: (employee: Employee) => void;
  deleteEmployee: (id: string) => void;
  updateEmployee: (id: string, employee: Employee) => void;
};

type DepartmentsStore = {
  departments: Department[];
  setDepartments: (departments: Department[]) => void;
  addDepartment: (department: Department) => void;
  deleteDepartment: (id: string) => void;
  updateDepartment: (id: string, department: Department) => void;
};

type MenuItemsStore = {
  menuItems: MenuItem[];
  setMenuItems: (menuItems: MenuItem[]) => void;
  addMenuItem: (menuItem: MenuItem) => void;
  deleteMenuItem: (id: string) => void;
  updateMenuItem: (id: string, menuItem: MenuItem) => void;
};

type MenuCategoriesStore = {
  menuCategories: MenuCategory[];
  setMenuCategories: (menuCategories: MenuCategory[]) => void;
  addMenuCategory: (menuCategory: MenuCategory) => void;
  deleteMenuCategory: (id: string) => void;
  updateMenuCategory: (id: string, menuCategory: MenuCategory) => void;
};

type OrdersStore = {
  orders: Order[];
  setOrders: (orders: Order[]) => void;
  addOrder: (order: Order) => void;
  deleteOrder: (id: string) => void;
  updateOrder: (id: string, order: Order) => void;
  updateOrderStatus: (id: string, status: OrderStatus) => void;
  updatePaymentStatus: (id: string, paymentStatus: PaymentStatus) => void;
};

type IngredientsStore = {
  ingredients: Ingredient[];
  setIngredients: (ingredients: Ingredient[]) => void;
  addIngredient: (ingredient: Ingredient) => void;
  deleteIngredient: (id: string) => void;
  updateIngredient: (id: string, ingredient: Ingredient) => void;
};

type IngredientCategoriesStore = {
  ingredientCategories: IngredientCategory[];
  setIngredientCategories: (categories: IngredientCategory[]) => void;
  addIngredientCategory: (category: IngredientCategory) => void;
  deleteIngredientCategory: (id: string) => void;
  updateIngredientCategory: (id: string, category: IngredientCategory) => void;
};

type TablesStore = {
  tables: Table[];
  setTables: (tables: Table[]) => void;
  addTable: (table: Table) => void;
  deleteTable: (id: string) => void;
  updateTable: (id: string, table: Partial<Table>) => void; // Allow partial updates
  updateTableStatus: (id: string, status: Table['status']) => void;
};

type SpacesStore = {
  spaces: Space[];
  setSpaces: (spaces: Space[]) => void;
  addSpace: (space: Space) => void;
  deleteSpace: (id: string) => void;
  updateSpace: (id: string, space: Partial<Space>) => void;
};

type SuppliersStore = {
  suppliers: Supplier[];
  setSuppliers: (suppliers: Supplier[]) => void;
  addSupplier: (supplier: Supplier) => void;
  deleteSupplier: (id: string) => void;
  updateSupplier: (supplier: Supplier) => void;
};

type AllStores = DepartmentsStore &
  SpacesStore &
  EmployeesStore &
  RolesStore &
  MenuItemsStore &
  MenuCategoriesStore &
  OrdersStore &
  IngredientsStore &
  IngredientCategoriesStore &
  TablesStore &
  SuppliersStore;

export const useDepartmentsStore = create<DepartmentsStore>((set) => ({
  departments: initialDepartments,
  setDepartments: (departments) => set({ departments }),
  addDepartment: (department) =>
    set((state) => ({ departments: [...state.departments, department] })),
  deleteDepartment: (id) =>
    set((state) => ({
      departments: state.departments.filter(
        (department) => department.id !== id
      ),
    })),
  updateDepartment: (id, department) =>
    set((state) => ({
      departments: state.departments.map((d) => (d.id === id ? department : d)),
    })),
}));

export const useSpacesStore = create<SpacesStore>((set) => ({
  spaces: initialSpaces,
  setSpaces: (spaces) => set({ spaces }),
  addSpace: (space) =>
    set((state) => ({ spaces: [...state.spaces, space] })),
  deleteSpace: (id) =>
    set((state) => ({
      spaces: state.spaces.filter((space) => space.id !== id),
    })),
  updateSpace: (id, spaceUpdate) =>
    set((state) => ({
      spaces: state.spaces.map((space) =>
        space.id === id
          ? { ...space, ...spaceUpdate, updatedAt: new Date().toISOString() }
          : space
      ),
    })),
}));

export const useEmployeesStore = create<EmployeesStore>((set) => ({
  employees: initialEmployees,
  setEmployees: (employees) => set({ employees }),
  addEmployee: (employee) =>
    set((state) => ({ employees: [...state.employees, employee] })),
  deleteEmployee: (id) =>
    set((state) => ({
      employees: state.employees.filter((employee) => employee.id !== id),
    })),
  updateEmployee: (id, employee) =>
    set((state) => ({
      employees: state.employees.map((e) => (e.id === id ? employee : e)),
    })),
}));

export const useRolesStore = create<RolesStore>((set) => ({
  roles: initialRoles,
  setRoles: (roles) => set({ roles }),
  addRole: (role) => set((state) => ({ roles: [...state.roles, role] })),
  deleteRole: (id) =>
    set((state) => ({
      roles: state.roles.filter((role) => role.id !== id),
    })),
  updateRole: (id, role) =>
    set((state) => ({
      roles: state.roles.map((r) => (r.id === id ? role : r)),
    })),
}));

export const useMenuItemsStore = create<MenuItemsStore>((set) => ({
  menuItems: initialMenuItems,
  setMenuItems: (menuItems) => set({ menuItems }),
  addMenuItem: (menuItem) =>
    set((state) => ({ menuItems: [...state.menuItems, menuItem] })),
  deleteMenuItem: (id) =>
    set((state) => ({
      menuItems: state.menuItems.filter((menuItem) => menuItem.id !== id),
    })),
  updateMenuItem: (id, menuItem) =>
    set((state) => ({
      menuItems: state.menuItems.map((m) => (m.id === id ? menuItem : m)),
    })),
}));

export const useMenuCategoriesStore = create<MenuCategoriesStore>((set) => ({
  menuCategories: initialMenuCategories,
  setMenuCategories: (menuCategories) => set({ menuCategories }),
  addMenuCategory: (menuCategory) =>
    set((state) => ({
      menuCategories: [...state.menuCategories, menuCategory],
    })),
  deleteMenuCategory: (id) =>
    set((state) => ({
      menuCategories: state.menuCategories.filter(
        (menuCategory) => menuCategory.id !== id
      ),
    })),
  updateMenuCategory: (id, menuCategory) =>
    set((state) => ({
      menuCategories: state.menuCategories.map((c) =>
        c.id === id ? menuCategory : c
      ),
    })),
}));

export const useOrdersStore = create<OrdersStore>((set) => ({
  orders: initialOrders,
  setOrders: (orders) => set({ orders }),
  addOrder: (order) => set((state) => ({ orders: [...state.orders, order] })),
  deleteOrder: (id) =>
    set((state) => ({
      orders: state.orders.filter((order) => order.id !== id),
    })),
  updateOrder: (id, order) =>
    set((state) => ({
      orders: state.orders.map((o) => (o.id === id ? order : o)),
    })),
  updateOrderStatus: (id, status) =>
    set((state) => ({
      orders: state.orders.map((o) =>
        o.id === id ? { ...o, status, updatedAt: new Date().toISOString() } : o
      ),
    })),
  updatePaymentStatus: (id, paymentStatus) =>
    set((state) => ({
      orders: state.orders.map((o) =>
        o.id === id
          ? { ...o, paymentStatus, updatedAt: new Date().toISOString() }
          : o
      ),
    })),
}));

export const useIngredientsStore = create<IngredientsStore>((set) => ({
  ingredients: initialIngredients,
  setIngredients: (ingredients) => set({ ingredients }),
  addIngredient: (ingredient) =>
    set((state) => ({ ingredients: [...state.ingredients, ingredient] })),
  deleteIngredient: (id) =>
    set((state) => ({
      ingredients: state.ingredients.filter(
        (ingredient) => ingredient.id !== id
      ),
    })),
  updateIngredient: (id, ingredient) =>
    set((state) => ({
      ingredients: state.ingredients.map((i) => (i.id === id ? ingredient : i)),
    })),
}));

export const useIngredientCategoriesStore = create<IngredientCategoriesStore>(
  (set) => ({
    ingredientCategories: initialIngredientCategories,
    setIngredientCategories: (ingredientCategories) =>
      set({ ingredientCategories }),
    addIngredientCategory: (category) =>
      set((state) => ({
        ingredientCategories: [...state.ingredientCategories, category],
      })),
    deleteIngredientCategory: (id) =>
      set((state) => ({
        ingredientCategories: state.ingredientCategories.filter(
          (category) => category.id !== id
        ),
      })),
    updateIngredientCategory: (id, category) =>
      set((state) => ({
        ingredientCategories: state.ingredientCategories.map((c) =>
          c.id === id ? category : c
        ),
      })),
  })
);

export const useTablesStore = create<TablesStore>((set) => ({
  tables: initialTables,
  setTables: (tables) => set({ tables }),
  addTable: (table) =>
    set((state) => ({ tables: [...state.tables, table] })),
  deleteTable: (id) =>
    set((state) => ({
      tables: state.tables.filter((table) => table.id !== id),
    })),
  updateTable: (id, tableUpdate) =>
    set((state) => ({
      tables: state.tables.map((table) =>
        table.id === id
          ? { ...table, ...tableUpdate, updatedAt: new Date().toISOString() }
          : table
      ),
    })),
  updateTableStatus: (id, status) =>
    set((state) => ({
      tables: state.tables.map((table) =>
        table.id === id
          ? { ...table, status, updatedAt: new Date().toISOString() }
          : table
      ),
    })),
}));

export const useSuppliersStore = create<SuppliersStore>((set) => ({
  suppliers: initialSuppliers,
  setSuppliers: (suppliers) => set({ suppliers }),
  addSupplier: (supplier) =>
    set((state) => ({ suppliers: [...state.suppliers, supplier] })),
  deleteSupplier: (id) =>
    set((state) => ({
      suppliers: state.suppliers.filter((supplier) => supplier.id !== id),
    })),
  updateSupplier: (supplier) =>
    set((state) => ({
      suppliers: state.suppliers.map((s) =>
        s.id === supplier.id ? supplier : s
      ),
    })),
}));
