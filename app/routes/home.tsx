import type { Route } from './+types/home';
import { Link } from 'react-router';

export function meta({}: Route.MetaArgs) {
  return [
    { title: 'ALP AI' },
    {
      name: 'description',
      content: 'Your AI-powered Restaurant Management System',
    },
  ];
}

export default function Home() {
  return (
    <main className='flex items-center justify-center pt-16 pb-4'>
      <div className='flex-1 flex flex-col items-center gap-16 min-h-0'>
        <header className='flex flex-col items-center gap-9'></header>
        <p className='max-w-[600px] text-center'>
          Your AI-powered Restaurant Management System
          <br />
          Bring intellegence to you workflow
        </p>
        <Link className='button' to='/dashboard'>
          Dashboard
        </Link>
        <div className='flex gap-4'>
          <Link className='button' to='/login'>
            Login
          </Link>
          <>{' or '}</>
          <Link className='button' to='/register'>
            Register
          </Link>
        </div>
      </div>
    </main>
  );
}
