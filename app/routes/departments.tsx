import DepartmentsPage from '@/components/departments/Departments';

// export async function clientLoader() {
//   const response = await fetch('http://localhost:3300/graphql', {
//     method: 'POST',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     body: JSON.stringify({
//       query: `
//         query Departments {
//           departments {
//             id
//             name
//           }
//         }
//       `,
//       variables: {},
//     }),
//   });
//   return response.json();
// }

export default function Departments() {
  return <DepartmentsPage />;
}
