import { useLocation, useNavigate } from 'react-router';
import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Home, ArrowLeft } from 'lucide-react';

export default function NotFoundPage() {
  const location = useLocation();
  const navigation = useNavigate();

  useEffect(() => {
    console.error(
      '404 Error: User attempted to access non-existent route within layout:',
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className='flex flex-col items-center justify-center h-full text-center space-y-6'>
      <div className='space-y-2'>
        <h1 className='text-4xl font-bold tracking-tight'>404</h1>
        <h2 className='text-2xl font-semibold tracking-tight'>
          Page not found
        </h2>
        <p className='text-muted-foreground'>
          Sorry, we couldn't find the page you're looking for.
        </p>
      </div>

      <div className='flex flex-col sm:flex-row gap-2 mt-6'>
        <Button
          variant='outline'
          onClick={() => navigation(-1)}
          className='flex items-center gap-2'
        >
          <ArrowLeft className='h-4 w-4' />
          Go Back
        </Button>
        <Button
          onClick={() => navigation('/dashboard')}
          className='flex items-center gap-2'
        >
          <Home className='h-4 w-4' />
          Back to Dashboard
        </Button>
      </div>

      <div className='mt-8 text-sm text-muted-foreground'>
        <p>Path: {location.pathname}</p>
      </div>
    </div>
  );
}
