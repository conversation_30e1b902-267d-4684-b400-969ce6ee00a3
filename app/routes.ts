import {
  type RouteConfig,
  index,
  route,
  layout,
  prefix,
} from '@react-router/dev/routes';

export default [
  index('routes/home.tsx'),
  route('login', 'routes/login.tsx'),
  // route('/register', 'routes/register.tsx'),
  ...prefix('dashboard', [
    layout('components/layout/Layout.tsx', [
      index('routes/dashboard.tsx'),
      route('employees', 'routes/employees.tsx'),
      route('departments', 'routes/departments.tsx'),
      route('roles', 'routes/roles.tsx'),
      route('spaces', 'routes/spaces.tsx'), // Add spaces route
      route('menu', 'routes/menu.tsx'),
      route('ingredients', 'routes/ingredients.tsx'),
      route('ingredient-categories', 'routes/ingredient-categories.tsx'),
      route('orders', 'routes/orders.tsx'),
      route('tables', 'routes/tables.tsx'), 
      route('suppliers', 'routes/suppliers.tsx'),

      route('*', 'routes/not-found.tsx'),
    ]),
  ]),
  route('*', 'components/errors/NotFound.tsx'),
] satisfies RouteConfig;
