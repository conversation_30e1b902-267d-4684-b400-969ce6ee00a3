/**
 * Flat design overrides for shadcn components
 */

/* Component-specific overrides */
.card,
.dialog-content,
.popover-content,
.dropdown-content,
.select-content,
.command-dialog,
.sheet-content,
.tooltip-content {
  box-shadow: none !important;
  border-width: 1px !important;
  border-style: solid !important;
}

/* Button overrides */
.btn,
button[class*='btn-'],
[type='button'],
[type='submit'],
[type='reset'] {
  box-shadow: none !important;
  text-shadow: none !important;
  border-width: 1px !important;
  border-style: solid !important;
}

/* Form elements */
input,
select,
textarea,
.input,
.select,
.textarea {
  box-shadow: none !important;
  border-width: 1px !important;
  border-style: solid !important;
}

/* Navigation elements */
.navbar,
.sidebar,
.tabs,
.tab {
  box-shadow: none !important;
}

/* Table elements */
table,
th,
td {
  border-width: 1px !important;
  border-style: solid !important;
}

/* Remove all shadows */
[class*='shadow'] {
  box-shadow: none !important;
}

/* Remove hover effects that add depth */
*:hover {
  box-shadow: none !important;
}

/* Specific shadcn component overrides */
.accordion-content,
.alert,
.avatar,
.badge,
.calendar,
.checkbox,
.collapsible-content,
.command-input,
.context-menu-content,
.hover-card-content,
.menubar-content,
.navigation-menu-content,
.radio-group,
.scroll-area,
.select-trigger,
.slider,
.switch,
.tabs,
.toast,
.toggle {
  box-shadow: none !important;
  border-width: 1px !important;
  border-style: solid !important;
}
